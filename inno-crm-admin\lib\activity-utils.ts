// Utility functions for activity logging and display

export interface ActivityLogEntry {
  id: string
  userId: string
  userRole: string
  action: string
  resource: string
  resourceId?: string
  details?: any
  ipAddress?: string
  userAgent?: string
  createdAt: string
  user: {
    id: string
    name: string
    email?: string
    role: string
  }
}

// Format activity action for display
export function formatActivityAction(action: string): string {
  const actionMap: { [key: string]: string } = {
    'CREATE': 'Created',
    'UPDATE': 'Updated',
    'DELETE': 'Deleted',
    'LOGIN': 'Logged in',
    'LOGOUT': 'Logged out',
    'CONTACT': 'Contacted',
    'COMPLETE': 'Completed',
    'VIEW': 'Viewed',
    'EXPORT': 'Exported',
  }
  
  return actionMap[action] || action
}

// Format resource name for display
export function formatResourceName(resource: string): string {
  const resourceMap: { [key: string]: string } = {
    'student': 'Student',
    'lead': 'Lead',
    'payment': 'Payment',
    'group': 'Group',
    'enrollment': 'Enrollment',
    'assessment': 'Assessment',
    'teacher': 'Teacher',
    'course': 'Course',
    'class': 'Class',
    'attendance': 'Attendance',
    'user': 'User',
    'auth': 'Authentication',
  }
  
  return resourceMap[resource] || resource.charAt(0).toUpperCase() + resource.slice(1)
}

// Get activity description
export function getActivityDescription(log: ActivityLogEntry): string {
  const action = formatActivityAction(log.action)
  const resource = formatResourceName(log.resource)
  const userName = log.user.name
  
  // Special cases for specific actions
  switch (log.action) {
    case 'LOGIN':
      return `${userName} logged into the system`
    case 'LOGOUT':
      return `${userName} logged out of the system`
    case 'CONTACT':
      if (log.resource === 'lead') {
        const leadName = log.details?.leadName || 'a lead'
        return `${userName} contacted ${leadName}`
      }
      break
    case 'CREATE':
      if (log.resource === 'student') {
        const studentName = log.details?.studentName || 'a student'
        return `${userName} created student record for ${studentName}`
      }
      if (log.resource === 'payment') {
        const amount = log.details?.amount || 'payment'
        return `${userName} created a payment record (${amount})`
      }
      break
    case 'COMPLETE':
      if (log.resource === 'assessment') {
        const studentName = log.details?.studentName || 'a student'
        const type = log.details?.type || 'assessment'
        return `${userName} completed ${type.toLowerCase().replace('_', ' ')} for ${studentName}`
      }
      break
  }
  
  // Default format
  return `${userName} ${action.toLowerCase()} ${resource.toLowerCase()}`
}

// Get activity icon based on action
export function getActivityIcon(action: string): string {
  const iconMap: { [key: string]: string } = {
    'CREATE': '➕',
    'UPDATE': '✏️',
    'DELETE': '🗑️',
    'LOGIN': '🔐',
    'LOGOUT': '🚪',
    'CONTACT': '📞',
    'COMPLETE': '✅',
    'VIEW': '👁️',
    'EXPORT': '📤',
  }
  
  return iconMap[action] || '📝'
}

// Get activity priority/importance level
export function getActivityPriority(log: ActivityLogEntry): 'low' | 'medium' | 'high' {
  // High priority actions
  if (['DELETE', 'LOGIN', 'LOGOUT'].includes(log.action)) {
    return 'high'
  }
  
  // Medium priority actions
  if (['CREATE', 'CONTACT', 'COMPLETE'].includes(log.action)) {
    return 'medium'
  }
  
  // Low priority actions (UPDATE, VIEW, etc.)
  return 'low'
}

// Format activity details for display
export function formatActivityDetails(details: any): string {
  if (!details) return ''
  
  try {
    // Handle common detail formats
    if (details.changes) {
      const changes = Object.keys(details.changes)
      return `Modified: ${changes.join(', ')}`
    }
    
    if (details.studentName) {
      return `Student: ${details.studentName}`
    }
    
    if (details.leadName) {
      return `Lead: ${details.leadName}`
    }
    
    if (details.amount) {
      return `Amount: ${details.amount}`
    }
    
    if (details.type) {
      return `Type: ${details.type.replace('_', ' ')}`
    }
    
    // Fallback to JSON string for complex objects
    return JSON.stringify(details, null, 2)
  } catch (error) {
    return 'Details unavailable'
  }
}

// Get time ago string
export function getTimeAgo(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) {
    return 'Just now'
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  }
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
  
  const diffInWeeks = Math.floor(diffInDays / 7)
  if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`
  }
  
  return date.toLocaleDateString()
}

// Filter activities by criteria
export function filterActivities(
  activities: ActivityLogEntry[],
  filters: {
    search?: string
    role?: string
    action?: string
    resource?: string
    startDate?: string
    endDate?: string
  }
): ActivityLogEntry[] {
  return activities.filter(activity => {
    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      const matchesSearch = 
        activity.user.name.toLowerCase().includes(searchLower) ||
        activity.action.toLowerCase().includes(searchLower) ||
        activity.resource.toLowerCase().includes(searchLower) ||
        getActivityDescription(activity).toLowerCase().includes(searchLower)
      
      if (!matchesSearch) return false
    }
    
    // Role filter
    if (filters.role && activity.userRole !== filters.role) {
      return false
    }
    
    // Action filter
    if (filters.action && activity.action !== filters.action) {
      return false
    }
    
    // Resource filter
    if (filters.resource && activity.resource !== filters.resource) {
      return false
    }
    
    // Date filters
    if (filters.startDate) {
      const activityDate = new Date(activity.createdAt)
      const startDate = new Date(filters.startDate)
      if (activityDate < startDate) return false
    }
    
    if (filters.endDate) {
      const activityDate = new Date(activity.createdAt)
      const endDate = new Date(filters.endDate)
      endDate.setHours(23, 59, 59, 999) // End of day
      if (activityDate > endDate) return false
    }
    
    return true
  })
}

// Group activities by date
export function groupActivitiesByDate(activities: ActivityLogEntry[]): { [date: string]: ActivityLogEntry[] } {
  return activities.reduce((groups, activity) => {
    const date = new Date(activity.createdAt).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(activity)
    return groups
  }, {} as { [date: string]: ActivityLogEntry[] })
}

// Get activity statistics
export function getActivityStatistics(activities: ActivityLogEntry[]) {
  const stats = {
    total: activities.length,
    byAction: {} as { [action: string]: number },
    byResource: {} as { [resource: string]: number },
    byRole: {} as { [role: string]: number },
    byUser: {} as { [userId: string]: { name: string; count: number } },
    today: 0,
    thisWeek: 0,
    thisMonth: 0,
  }
  
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
  const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
  
  activities.forEach(activity => {
    const activityDate = new Date(activity.createdAt)
    
    // Count by action
    stats.byAction[activity.action] = (stats.byAction[activity.action] || 0) + 1
    
    // Count by resource
    stats.byResource[activity.resource] = (stats.byResource[activity.resource] || 0) + 1
    
    // Count by role
    stats.byRole[activity.userRole] = (stats.byRole[activity.userRole] || 0) + 1
    
    // Count by user
    if (!stats.byUser[activity.userId]) {
      stats.byUser[activity.userId] = { name: activity.user.name, count: 0 }
    }
    stats.byUser[activity.userId].count++
    
    // Time-based counts
    if (activityDate >= today) {
      stats.today++
    }
    if (activityDate >= weekAgo) {
      stats.thisWeek++
    }
    if (activityDate >= monthAgo) {
      stats.thisMonth++
    }
  })
  
  return stats
}
