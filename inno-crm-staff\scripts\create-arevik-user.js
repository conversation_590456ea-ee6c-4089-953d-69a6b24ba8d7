require('dotenv').config({ path: '.env.local' });
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createArevikUser() {
  try {
    console.log('Creating Arevik user...');

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { phone: '+998912345678' }
    });

    if (existingUser) {
      console.log('User Arevik already exists:', existingUser);
      return;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('Arevik0106$', 12);

    // Create the user
    const user = await prisma.user.create({
      data: {
        phone: '+998912345678',
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        role: 'TEACHER', // Staff role that can access staff server
        password: hashedPassword,
      }
    });

    console.log('Successfully created Arevik user:', {
      id: user.id,
      phone: user.phone,
      name: user.name,
      email: user.email,
      role: user.role,
    });

  } catch (error) {
    console.error('Error creating Arevik user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createArevikUser();
