#!/usr/bin/env node

/**
 * Test script for Granular Teacher Tier Progression System
 * Tests the slot-specific 80% capacity rule implementation
 */

const BASE_URL = 'http://localhost:3001'

async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`HTTP ${response.status}: ${error}`)
  }

  return response.json()
}

async function testGranularSlotAnalysis() {
  console.log('🎯 Testing Granular Slot-based Tier Progression...')
  
  try {
    // Test Groups API slot analysis
    console.log('  📊 Fetching slot-based tier analysis...')
    const groupsData = await makeRequest('/api/groups?branch=main')
    
    if (groupsData.slotTierAnalysis) {
      console.log('  ✅ Slot-based tier analysis available:')
      console.log(`     Found ${groupsData.slotTierAnalysis.length} unique slots`)
      
      groupsData.slotTierAnalysis.forEach(slot => {
        console.log(`\n     📍 Slot: ${slot.courseLevel} - ${slot.days} - ${slot.time}`)
        console.log(`        Total groups: ${slot.totalGroups}`)
        console.log(`        Available tiers: ${slot.availableTiers.join(', ') || 'None'}`)
        
        slot.tierUtilization.forEach(tier => {
          const status = tier.utilizationRate >= 80 ? '🟢 READY' : '🟡 FILLING'
          const available = slot.availableTiers.includes(tier.tier) ? '✅ AVAILABLE' : '🔒 LOCKED'
          console.log(`           - ${tier.tier}: ${tier.utilizationRate.toFixed(1)}% (${tier.groupCount} groups) ${status} ${available}`)
        })
      })
      
      // Test specific scenario: A1 M/W/F 14:00-15:30
      console.log('\n  🔍 Testing specific scenario: A1 M/W/F 14:00-15:30')
      const a1MwfSlot = groupsData.slotTierAnalysis.find(slot =>
        slot.courseLevel === 'A1' &&
        slot.days === 'MWF' &&
        slot.time.includes('14:00')
      )

      if (a1MwfSlot) {
        console.log('     ✅ Found A1 M/W/F 14:00 slot:')

        const aLevelTier = a1MwfSlot.tierUtilization.find(t => t.tier === 'A_LEVEL')
        const bLevelTier = a1MwfSlot.tierUtilization.find(t => t.tier === 'B_LEVEL')

        if (aLevelTier) {
          console.log(`        - Parviz (A-Level): ${aLevelTier.utilizationRate.toFixed(1)}% utilized`)
          
          if (aLevelTier.utilizationRate >= 80) {
            console.log('        ✅ A-Level is 80%+ utilized - B-Level should be available')
            if (a1MwfSlot.availableTiers.includes('B_LEVEL')) {
              console.log('        ✅ B-Level correctly available for this slot')
            } else {
              console.log('        ❌ B-Level should be available but is not')
            }
          } else {
            console.log('        🟡 A-Level not yet 80% - B-Level should be locked')
            if (!a1MwfSlot.availableTiers.includes('B_LEVEL')) {
              console.log('        ✅ B-Level correctly locked for this slot')
            } else {
              console.log('        ❌ B-Level should be locked but is available')
            }
          }
        }
        
        if (bLevelTier) {
          console.log(`        - Otabek (B-Level): ${bLevelTier.utilizationRate.toFixed(1)}% utilized`)
        }
      } else {
        console.log('     ⚠️  A1 M/W/F 14:00 slot not found in data')
      }
      
    } else {
      console.log('  ⚠️  No slot-based tier analysis found')
    }
    
  } catch (error) {
    console.error('  ❌ Error testing granular slot analysis:', error.message)
  }
}

async function testLeadAssignmentGranular() {
  console.log('\n👥 Testing Lead Assignment Granular Progression...')
  
  try {
    // Test with a mock lead ID
    const testLeadId = 'test-lead-id'
    
    console.log('  🔍 Fetching slot-filtered groups for lead assignment...')
    const assignmentData = await makeRequest(`/api/leads/${testLeadId}/assign-group?branch=main`)
    
    if (assignmentData.groups && assignmentData.slotAnalysis) {
      console.log('  ✅ Lead assignment slot analysis received:')
      console.log(`     - Available groups: ${assignmentData.groups.length}`)
      console.log(`     - Analyzed slots: ${assignmentData.slotAnalysis.length}`)
      
      // Show slot-specific filtering results
      console.log('\n  📊 Slot-specific filtering results:')
      assignmentData.slotAnalysis.forEach(slot => {
        console.log(`\n     📍 ${slot.courseLevel} - ${slot.days} - ${slot.time}`)
        console.log(`        Available groups in slot: ${slot.totalAvailable}`)
        
        slot.tierUtilization.forEach(tier => {
          console.log(`           - ${tier.tier}: ${tier.availableGroups}/${tier.groupCount} available (${tier.utilizationRate.toFixed(1)}% utilized)`)
        })
      })
      
      // Verify granular filtering logic
      console.log('\n  🔄 Verifying granular filtering logic...')
      
      // Group available groups by slot
      const groupsBySlot = {}
      assignmentData.groups.forEach(group => {
        // Parse schedule to determine slot
        const scheduleStr = JSON.stringify(group.schedule)
        const timeMatch = scheduleStr.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/)
        const time = timeMatch ? `${timeMatch[1]}:${timeMatch[2]}-${timeMatch[3]}:${timeMatch[4]}` : 'Unknown'
        
        const days = scheduleStr.toLowerCase().includes('monday') && 
                    scheduleStr.toLowerCase().includes('wednesday') && 
                    scheduleStr.toLowerCase().includes('friday') ? 'MWF' :
                    scheduleStr.toLowerCase().includes('tuesday') && 
                    scheduleStr.toLowerCase().includes('thursday') && 
                    scheduleStr.toLowerCase().includes('saturday') ? 'TTS' : 'Other'
        
        const slotKey = `${group.course.level}-${days}-${time}`
        
        if (!groupsBySlot[slotKey]) {
          groupsBySlot[slotKey] = []
        }
        groupsBySlot[slotKey].push(group)
      })
      
      Object.entries(groupsBySlot).forEach(([slotKey, groups]) => {
        console.log(`\n        Slot ${slotKey}: ${groups.length} available groups`)
        
        // Check if tier progression is correctly applied
        const tierCounts = {}
        groups.forEach(group => {
          const tier = group.teacher.tier || 'NEW'
          tierCounts[tier] = (tierCounts[tier] || 0) + 1
        })
        
        Object.entries(tierCounts).forEach(([tier, count]) => {
          console.log(`           - ${tier}: ${count} groups available`)
        })
      })
      
    } else {
      console.log('  ⚠️  No slot analysis data found')
    }
    
  } catch (error) {
    console.error('  ❌ Error testing lead assignment granular progression:', error.message)
  }
}

async function testSpecificScenario() {
  console.log('\n🎯 Testing Specific Scenario: A1 M/W/F 14:00-15:30...')
  
  try {
    console.log('  📝 Scenario:')
    console.log('     - Parviz Adashov (A-Level) teaching A1 M/W/F 14:00-15:30')
    console.log('     - Otabek Halimov (B-Level) wants to teach same slot')
    console.log('     - Rule: Otabek only available when Parviz reaches 80% capacity')
    
    const groupsData = await makeRequest('/api/groups?branch=main')
    
    if (groupsData.slotTierAnalysis) {
      // Find the specific slot
      const targetSlot = groupsData.slotTierAnalysis.find(slot => 
        slot.courseLevel === 'A1' && 
        slot.days === 'MWF' && 
        slot.time.includes('14:00')
      )
      
      if (targetSlot) {
        console.log('  ✅ Found target slot in analysis')
        
        const aLevel = targetSlot.tierUtilization.find(t => t.tier === 'A_LEVEL')
        const bLevel = targetSlot.tierUtilization.find(t => t.tier === 'B_LEVEL')
        
        if (aLevel) {
          console.log(`     - Parviz (A-Level): ${aLevel.utilizationRate.toFixed(1)}% capacity`)

          if (aLevel.utilizationRate >= 80) {
            console.log('     ✅ Parviz has reached 80% capacity')
            if (targetSlot.availableTiers.includes('B_LEVEL')) {
              console.log('     ✅ Otabek (B-Level) is now available for this slot')
            } else {
              console.log('     ❌ Otabek should be available but is not')
            }
          } else {
            console.log(`     🟡 Parviz needs ${(80 - aLevel.utilizationRate).toFixed(1)}% more to unlock B-Level`)
            if (!targetSlot.availableTiers.includes('B_LEVEL')) {
              console.log('     ✅ Otabek correctly locked until Parviz reaches 80%')
            } else {
              console.log('     ❌ Otabek should be locked but is available')
            }
          }
        }
        
        if (bLevel) {
          console.log(`     - Otabek (B-Level): ${bLevel.utilizationRate.toFixed(1)}% capacity`)
        }
        
      } else {
        console.log('  ⚠️  Target slot A1 M/W/F 14:00-15:30 not found')
      }
    }
    
  } catch (error) {
    console.error('  ❌ Error testing specific scenario:', error.message)
  }
}

async function runGranularTests() {
  console.log('🚀 Starting Granular Teacher Tier Progression Tests...\n')
  
  await testGranularSlotAnalysis()
  await testLeadAssignmentGranular()
  await testSpecificScenario()
  
  console.log('\n✅ Granular Teacher Tier Progression Tests Completed!')
  console.log('\n📋 Summary:')
  console.log('   - Slot-based tier analysis implemented')
  console.log('   - 80% capacity rule applied per course level, time, and day combination')
  console.log('   - Lead assignment respects slot-specific tier availability')
  console.log('   - Independent progression for each unique slot')
  console.log('   - Parviz/Otabek scenario properly handled per slot')
}

// Run tests if this script is executed directly
if (require.main === module) {
  runGranularTests().catch(console.error)
}

module.exports = {
  testGranularSlotAnalysis,
  testLeadAssignmentGranular,
  testSpecificScenario,
  runGranularTests
}
