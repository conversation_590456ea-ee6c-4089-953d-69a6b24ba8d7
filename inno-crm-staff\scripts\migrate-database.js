#!/usr/bin/env node

/**
 * Database Migration Script
 * This script helps migrate the database schema to support the new assessment system
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting database migration...\n');

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env file not found!');
  console.log('Please create a .env file with your DATABASE_URL');
  console.log('Example: DATABASE_URL="postgresql://username:password@localhost:5432/database_name"');
  process.exit(1);
}

// Read .env file
const envContent = fs.readFileSync(envPath, 'utf8');
const databaseUrl = envContent.match(/DATABASE_URL="([^"]+)"/)?.[1];

if (!databaseUrl) {
  console.error('❌ DATABASE_URL not found in .env file!');
  console.log('Please add DATABASE_URL to your .env file');
  process.exit(1);
}

console.log('✅ Found DATABASE_URL in .env file');

try {
  // Step 1: Generate Prisma client
  console.log('📦 Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client generated successfully\n');

  // Step 2: Push schema to database
  console.log('🔄 Pushing schema to database...');
  execSync('npx prisma db push', { stdio: 'inherit' });
  console.log('✅ Database schema updated successfully\n');

  // Step 3: Optional - Run seed if it exists
  const seedPath = path.join(process.cwd(), 'prisma', 'seed.js');
  if (fs.existsSync(seedPath)) {
    console.log('🌱 Running database seed...');
    try {
      execSync('npx prisma db seed', { stdio: 'inherit' });
      console.log('✅ Database seeded successfully\n');
    } catch (error) {
      console.log('⚠️  Seed failed (this is optional)\n');
    }
  }

  console.log('🎉 Database migration completed successfully!');
  console.log('\n📋 Test User Credentials:');
  console.log('   Admin: +998901234567 / admin123 (Full access)');
  console.log('   Cashier: +998901234570 / cashier123 (Limited access)');
  console.log('   Teacher: +998905555555 / teacher123 (Test Administrator)');
  console.log('\nYou can now:');
  console.log('1. Run "npm run dev" to start the development server');
  console.log('2. Access the application at http://localhost:3001');
  console.log('3. Test the new role-based access control and assessment system');
  console.log('4. Login with different roles to verify restrictions work correctly');

} catch (error) {
  console.error('❌ Migration failed:', error.message);
  console.log('\nTroubleshooting:');
  console.log('1. Make sure PostgreSQL is running');
  console.log('2. Check your DATABASE_URL credentials');
  console.log('3. Ensure the database exists');
  console.log('4. Check if PostgreSQL is listening on the correct port');
  process.exit(1);
}
