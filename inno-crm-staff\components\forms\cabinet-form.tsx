'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Building, Hash, Users, MapPin, Package, FileText, Loader2 } from 'lucide-react'

const cabinetSchema = z.object({
  name: z.string().min(2, 'Cabinet name must be at least 2 characters'),
  number: z.string().min(1, 'Cabinet number is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1').max(100, 'Capacity cannot exceed 100'),
  floor: z.number().optional(),
  building: z.string().optional(),
  branch: z.string().min(1, 'Branch is required'),
  equipment: z.string().optional(),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
})

type CabinetFormData = z.infer<typeof cabinetSchema>

interface CabinetFormProps {
  initialData?: Partial<CabinetFormData>
  onSubmit: (data: CabinetFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
}

const branches = [
  { value: 'Main Branch', label: 'Main Branch' },
  { value: 'Branch', label: 'Branch' },
]

export default function CabinetForm({ initialData, onSubmit, onCancel, isEditing = false }: CabinetFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CabinetFormData>({
    resolver: zodResolver(cabinetSchema),
    defaultValues: {
      name: initialData?.name || '',
      number: initialData?.number || '',
      capacity: initialData?.capacity || 20,
      floor: initialData?.floor || undefined,
      building: initialData?.building || '',
      branch: initialData?.branch || '',
      equipment: initialData?.equipment || '',
      notes: initialData?.notes || '',
      isActive: initialData?.isActive ?? true,
    },
  })

  const isActive = watch('isActive')

  const handleFormSubmit = async (data: CabinetFormData) => {
    try {
      setIsSubmitting(true)
      setError(null)
      await onSubmit(data)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-4">
            <Building className="h-4 w-4 text-gray-500" />
            <h3 className="text-lg font-medium">Basic Information</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Cabinet Name *</Label>
              <div className="relative">
                <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="e.g., Computer Lab 1"
                  className="pl-10"
                />
              </div>
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="number">Cabinet Number *</Label>
              <div className="relative">
                <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="number"
                  {...register('number')}
                  placeholder="e.g., 101, A-205"
                  className="pl-10"
                />
              </div>
              {errors.number && (
                <p className="text-sm text-red-600">{errors.number.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="capacity">Capacity *</Label>
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="capacity"
                  type="number"
                  min="1"
                  max="100"
                  {...register('capacity', { valueAsNumber: true })}
                  placeholder="20"
                  className="pl-10"
                />
              </div>
              {errors.capacity && (
                <p className="text-sm text-red-600">{errors.capacity.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="branch">Branch *</Label>
              <Select
                value={watch('branch')}
                onValueChange={(value) => setValue('branch', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select branch" />
                </SelectTrigger>
                <SelectContent>
                  {branches.map((branch) => (
                    <SelectItem key={branch.value} value={branch.value}>
                      {branch.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.branch && (
                <p className="text-sm text-red-600">{errors.branch.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Location Information */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-4">
            <MapPin className="h-4 w-4 text-gray-500" />
            <h3 className="text-lg font-medium">Location Details</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="floor">Floor</Label>
              <Input
                id="floor"
                type="number"
                min="0"
                {...register('floor', { valueAsNumber: true })}
                placeholder="e.g., 1, 2, 3"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="building">Building</Label>
              <Input
                id="building"
                {...register('building')}
                placeholder="e.g., Main Building, Block A"
              />
            </div>
          </div>
        </div>

        {/* Equipment and Notes */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-4">
            <Package className="h-4 w-4 text-gray-500" />
            <h3 className="text-lg font-medium">Additional Information</h3>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="equipment">Equipment</Label>
              <Textarea
                id="equipment"
                {...register('equipment')}
                placeholder="List available equipment (projector, whiteboard, computers, etc.)"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                {...register('notes')}
                placeholder="Additional notes about the cabinet"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={isActive}
                onCheckedChange={(checked) => setValue('isActive', checked)}
              />
              <Label htmlFor="isActive">Active Cabinet</Label>
            </div>
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-2 pt-6 border-t">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isEditing ? 'Update Cabinet' : 'Create Cabinet'}
        </Button>
      </div>
    </form>
  )
}
