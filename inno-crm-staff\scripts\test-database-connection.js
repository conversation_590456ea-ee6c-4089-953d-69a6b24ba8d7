const { PrismaClient } = require('@prisma/client');

async function testDatabaseConnection() {
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL || "postgresql://crm_owner:<EMAIL>/crm?sslmode=require"
      }
    }
  });

  try {
    console.log('Testing database connection...');

    // Test basic connection
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Test table access
    const userCount = await prisma.user.count();
    console.log(`✅ Users table accessible - ${userCount} users found`);

    const leadCount = await prisma.lead.count();
    console.log(`✅ Leads table accessible - ${leadCount} leads found`);

    const courseCount = await prisma.course.count();
    console.log(`✅ Courses table accessible - ${courseCount} courses found`);

    const groupCount = await prisma.group.count();
    console.log(`✅ Groups table accessible - ${groupCount} groups found`);

    // Test enum values
    const leadStatuses = await prisma.$queryRaw`
      SELECT unnest(enum_range(NULL::"LeadStatus")) as status
    `;
    console.log('✅ Lead status enum values:', leadStatuses.map(s => s.status));

    console.log('\n🎉 All database tests passed successfully!');

  } catch (error) {
    console.error('❌ Database test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testDatabaseConnection()
    .then(() => {
      console.log('Database test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Database test failed:', error);
      process.exit(1);
    });
}

module.exports = { testDatabaseConnection };
