import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getNotificationService } from '@/lib/notifications'
import { prisma } from '@/lib/prisma'

const sendNotificationSchema = z.object({
  recipientId: z.string(),
  recipientType: z.enum(['student', 'teacher', 'parent']),
  type: z.enum(['enrollment', 'payment', 'reminder', 'completion', 'attendance']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  channels: z.array(z.enum(['sms', 'email', 'push'])).optional(),
  data: z.record(z.any()),
})

const bulkNotificationSchema = z.object({
  recipientIds: z.array(z.string()),
  recipientType: z.enum(['student', 'teacher', 'parent']),
  type: z.enum(['enrollment', 'payment', 'reminder', 'completion', 'attendance']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  channels: z.array(z.enum(['sms', 'email', 'push'])).optional(),
  data: z.record(z.any()),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const notificationService = getNotificationService()

    // Check if it's a bulk notification
    if (body.recipientIds && Array.isArray(body.recipientIds)) {
      const validatedData = bulkNotificationSchema.parse(body)
      
      // Get recipients
      const recipients = await getRecipients(validatedData.recipientIds, validatedData.recipientType)
      
      if (recipients.length === 0) {
        return NextResponse.json(
          { error: 'No valid recipients found' },
          { status: 400 }
        )
      }

      // Send bulk notification
      const results = await notificationService.sendBulkNotification(recipients, {
        type: validatedData.type,
        priority: validatedData.priority,
        channels: validatedData.channels,
        data: validatedData.data,
      })

      const successCount = results.filter(r => r.success).length

      return NextResponse.json({
        success: true,
        message: `Sent ${successCount}/${recipients.length} notifications successfully`,
        results,
      })
    } else {
      // Single notification
      const validatedData = sendNotificationSchema.parse(body)
      
      // Get recipient
      const recipients = await getRecipients([validatedData.recipientId], validatedData.recipientType)
      
      if (recipients.length === 0) {
        return NextResponse.json(
          { error: 'Recipient not found' },
          { status: 404 }
        )
      }

      const recipient = recipients[0]

      // Send notification
      const result = await notificationService.sendNotification(recipient, {
        type: validatedData.type,
        priority: validatedData.priority,
        channels: validatedData.channels,
        data: validatedData.data,
      })

      return NextResponse.json({
        success: result.success,
        message: result.success ? 'Notification sent successfully' : 'Failed to send notification',
        result,
      })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error sending notification:', error)
    return NextResponse.json(
      { error: 'Failed to send notification' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'templates') {
      // Return available notification templates
      return NextResponse.json({
        templates: {
          enrollment: {
            name: 'Enrollment Confirmation',
            description: 'Sent when a student enrolls in a course',
            requiredData: ['studentName', 'courseName', 'startDate', 'groupName'],
          },
          payment: {
            name: 'Payment Notification',
            description: 'Sent for payment confirmations or reminders',
            requiredData: ['studentName', 'amount', 'courseName'],
            optionalData: ['paymentMethod', 'transactionId', 'dueDate', 'isConfirmation'],
          },
          reminder: {
            name: 'Class Reminder',
            description: 'Sent before classes start',
            requiredData: ['studentName', 'courseName', 'time'],
          },
          completion: {
            name: 'Course Completion',
            description: 'Sent when a student completes a course',
            requiredData: ['studentName', 'courseName', 'completionDate'],
            optionalData: ['nextLevel'],
          },
          attendance: {
            name: 'Attendance Alert',
            description: 'Sent to parents when student is absent',
            requiredData: ['parentName', 'studentName', 'courseName'],
          },
        },
      })
    }

    if (action === 'test') {
      // Test notification service connectivity
      const notificationService = getNotificationService()
      
      // Test SMS service
      const smsTest = await testSMSService()
      
      // Test Email service
      const emailTest = await testEmailService()

      return NextResponse.json({
        sms: smsTest,
        email: emailTest,
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
  } catch (error) {
    console.error('Error in notifications GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getRecipients(ids: string[], type: string) {
  const recipients = []

  for (const id of ids) {
    let recipient = null

    switch (type) {
      case 'student':
        const student = await prisma.student.findUnique({
          where: { id },
          include: { user: true },
        })
        if (student) {
          recipient = {
            id: student.id,
            name: student.user.name,
            phone: student.user.phone,
            email: student.user.email || undefined,
          }
        }
        break

      case 'teacher':
        const teacher = await prisma.teacher.findUnique({
          where: { id },
          include: { user: true },
        })
        if (teacher) {
          recipient = {
            id: teacher.id,
            name: teacher.user.name,
            phone: teacher.user.phone,
            email: teacher.user.email || undefined,
          }
        }
        break

      case 'academic_manager':
        // Academic managers are stored as users with ACADEMIC_MANAGER role
        const academicManager = await prisma.user.findFirst({
          where: {
            id,
            role: 'ACADEMIC_MANAGER',
          },
        })
        if (academicManager) {
          recipient = {
            id: academicManager.id,
            name: academicManager.name,
            phone: academicManager.phone,
            email: academicManager.email || undefined,
          }
        }
        break
    }

    if (recipient) {
      recipients.push(recipient)
    }
  }

  return recipients
}

async function testSMSService(): Promise<{ available: boolean; provider?: string; error?: string }> {
  try {
    // Check if SMS environment variables are configured
    const smsProvider = process.env.SMS_PROVIDER
    const smsApiKey = process.env.SMS_API_KEY

    if (!smsProvider || !smsApiKey) {
      return {
        available: false,
        error: 'SMS service not configured (missing SMS_PROVIDER or SMS_API_KEY)',
      }
    }

    return {
      available: true,
      provider: smsProvider,
    }
  } catch (error) {
    return {
      available: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

async function testEmailService(): Promise<{ available: boolean; provider?: string; error?: string }> {
  try {
    // Check if Email environment variables are configured
    const emailProvider = process.env.EMAIL_PROVIDER
    const emailUser = process.env.EMAIL_USER
    const emailPassword = process.env.EMAIL_PASSWORD

    if (!emailProvider || !emailUser || !emailPassword) {
      return {
        available: false,
        error: 'Email service not configured (missing EMAIL_PROVIDER, EMAIL_USER, or EMAIL_PASSWORD)',
      }
    }

    return {
      available: true,
      provider: emailProvider,
    }
  } catch (error) {
    return {
      available: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}
