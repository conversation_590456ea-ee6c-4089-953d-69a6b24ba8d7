import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    // Fetch student with payment history
    const student = await prisma.student.findUnique({
      where: { id },
      include: {
        payments: {
          orderBy: { createdAt: 'desc' },
          take: 20, // Limit to last 20 payments
        },
      },
    })

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      )
    }

    // Calculate unpaid amount
    const unpaidPayments = student.payments.filter(p =>
      p.status === 'DEBT'
    )
    const unpaidAmount = unpaidPayments.reduce((sum, p) => sum + Number(p.amount), 0)

    return NextResponse.json({
      payments: student.payments,
      unpaidAmount,
      totalPayments: student.payments.length,
    })
  } catch (error) {
    console.error('Error fetching student payments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
