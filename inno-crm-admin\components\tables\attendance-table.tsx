"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { AttendanceForm } from "@/components/forms/attendance-form"
import { Search, Plus, Edit, Trash2, Download, Filter, Calendar } from "lucide-react"

interface Attendance {
  id: string
  studentId: string
  student: {
    user: {
      name: string
      phone: string
    }
  }
  class: {
    id: string
    date: string
    topic: string | null
    group: {
      name: string
      course: {
        name: string
      }
    }
    teacher: {
      user: {
        name: string
      }
    }
  }
  status: string
  notes: string | null
  createdAt: string
  updatedAt: string
}

interface AttendanceTableProps {
  initialData?: Attendance[]
}

export function AttendanceTable({ initialData = [] }: AttendanceTableProps) {
  const [attendances, setAttendances] = useState<Attendance[]>(initialData)
  const [filteredAttendances, setFilteredAttendances] = useState<Attendance[]>(initialData)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [groupFilter, setGroupFilter] = useState("")
  const [dateFilter, setDateFilter] = useState("")
  const [loading, setLoading] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingAttendance, setEditingAttendance] = useState<Attendance | null>(null)

  // Fetch attendance data
  const fetchAttendances = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/attendance")
      if (response.ok) {
        const data = await response.json()
        setAttendances(data)
        setFilteredAttendances(data)
      }
    } catch (error) {
      console.error("Error fetching attendances:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (initialData.length === 0) {
      fetchAttendances()
    }
  }, [initialData])

  // Filter attendances based on search and filters
  useEffect(() => {
    let filtered = attendances

    if (searchTerm) {
      filtered = filtered.filter(
        (attendance) =>
          attendance.student.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          attendance.student.user.phone.includes(searchTerm) ||
          attendance.class.group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          attendance.class.topic?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter) {
      filtered = filtered.filter((attendance) => attendance.status === statusFilter)
    }

    if (groupFilter) {
      filtered = filtered.filter((attendance) => attendance.class.group.name === groupFilter)
    }

    if (dateFilter) {
      const filterDate = new Date(dateFilter)
      filtered = filtered.filter((attendance) => {
        const classDate = new Date(attendance.class.date)
        return classDate.toDateString() === filterDate.toDateString()
      })
    }

    setFilteredAttendances(filtered)
  }, [attendances, searchTerm, statusFilter, groupFilter, dateFilter])

  // Get unique statuses and groups for filters
  const uniqueStatuses = [...new Set(attendances.map((attendance) => attendance.status))]
  const uniqueGroups = [...new Set(attendances.map((attendance) => attendance.class.group.name))]

  // Handle attendance deletion
  const handleDelete = async (attendanceId: string) => {
    if (!confirm("Are you sure you want to delete this attendance record?")) return

    try {
      const response = await fetch(`/api/attendance/${attendanceId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setAttendances(attendances.filter((attendance) => attendance.id !== attendanceId))
      } else {
        alert("Failed to delete attendance record")
      }
    } catch (error) {
      console.error("Error deleting attendance:", error)
      alert("Error deleting attendance record")
    }
  }

  // Handle form submission
  const handleFormSubmit = async (data: any) => {
    try {
      // Submit the attendance data to the API
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error('Failed to save attendance')
      }

      setIsCreateDialogOpen(false)
      setEditingAttendance(null)
      fetchAttendances()
    } catch (error) {
      console.error('Error saving attendance:', error)
      throw error
    }
  }

  // Export to CSV
  const exportToCSV = () => {
    const headers = [
      "Student Name",
      "Phone",
      "Group",
      "Course",
      "Teacher",
      "Class Date",
      "Topic",
      "Status",
      "Notes",
    ]
    const csvData = filteredAttendances.map((attendance) => [
      attendance.student.user.name,
      attendance.student.user.phone,
      attendance.class.group.name,
      attendance.class.group.course.name,
      attendance.class.teacher.user.name,
      new Date(attendance.class.date).toLocaleDateString(),
      attendance.class.topic || "",
      attendance.status,
      attendance.notes || "",
    ])

    const csvContent = [headers, ...csvData]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `attendance-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "PRESENT":
        return "default"
      case "ABSENT":
        return "destructive"
      case "LATE":
        return "secondary"
      case "EXCUSED":
        return "outline"
      default:
        return "secondary"
    }
  }

  // Calculate attendance statistics
  const totalRecords = filteredAttendances.length
  const presentCount = filteredAttendances.filter((a) => a.status === "PRESENT").length
  const absentCount = filteredAttendances.filter((a) => a.status === "ABSENT").length
  const lateCount = filteredAttendances.filter((a) => a.status === "LATE").length
  const excusedCount = filteredAttendances.filter((a) => a.status === "EXCUSED").length

  const attendanceRate = totalRecords > 0 ? ((presentCount + lateCount) / totalRecords * 100).toFixed(1) : "0"

  return (
    <div className="space-y-4">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <h2 className="text-2xl font-bold">Attendance Records</h2>
        <div className="flex gap-2">
          <Button onClick={exportToCSV} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Mark Attendance
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Mark Attendance</DialogTitle>
              </DialogHeader>
              <AttendanceForm onSubmit={handleFormSubmit} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-blue-600">Total Records</h3>
          <p className="text-2xl font-bold text-blue-900">{totalRecords}</p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-green-600">Present</h3>
          <p className="text-2xl font-bold text-green-900">{presentCount}</p>
        </div>
        <div className="bg-red-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-red-600">Absent</h3>
          <p className="text-2xl font-bold text-red-900">{absentCount}</p>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-yellow-600">Late</h3>
          <p className="text-2xl font-bold text-yellow-900">{lateCount}</p>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-purple-600">Attendance Rate</h3>
          <p className="text-2xl font-bold text-purple-900">{attendanceRate}%</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search attendance records..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Statuses</option>
          {uniqueStatuses.map((status) => (
            <option key={status} value={status}>
              {status}
            </option>
          ))}
        </select>
        <select
          value={groupFilter}
          onChange={(e) => setGroupFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Groups</option>
          {uniqueGroups.map((group) => (
            <option key={group} value={group}>
              {group}
            </option>
          ))}
        </select>
        <Input
          type="date"
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className="w-auto"
        />
        {(statusFilter || groupFilter || dateFilter || searchTerm) && (
          <Button
            variant="outline"
            onClick={() => {
              setStatusFilter("")
              setGroupFilter("")
              setDateFilter("")
              setSearchTerm("")
            }}
          >
            Clear Filters
          </Button>
        )}
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>Group</TableHead>
              <TableHead>Course</TableHead>
              <TableHead>Teacher</TableHead>
              <TableHead>Class Date</TableHead>
              <TableHead>Topic</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Notes</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  Loading attendance records...
                </TableCell>
              </TableRow>
            ) : filteredAttendances.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  No attendance records found
                </TableCell>
              </TableRow>
            ) : (
              filteredAttendances.map((attendance) => (
                <TableRow key={attendance.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{attendance.student.user.name}</div>
                      <div className="text-sm text-gray-500">
                        {attendance.student.user.phone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{attendance.class.group.name}</Badge>
                  </TableCell>
                  <TableCell>{attendance.class.group.course.name}</TableCell>
                  <TableCell>{attendance.class.teacher.user.name}</TableCell>
                  <TableCell>
                    {new Date(attendance.class.date).toLocaleDateString()}
                  </TableCell>
                  <TableCell>{attendance.class.topic || "N/A"}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(attendance.status)}>
                      {attendance.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{attendance.notes || "N/A"}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingAttendance(attendance)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Edit Attendance</DialogTitle>
                          </DialogHeader>
                          {editingAttendance && (
                            <AttendanceForm
                              initialData={{
                                classId: editingAttendance.class.id,
                                attendances: [{
                                  studentId: editingAttendance.studentId,
                                  status: editingAttendance.status as any,
                                  notes: editingAttendance.notes || undefined,
                                }]
                              }}
                              onSubmit={handleFormSubmit}
                              isEditing={true}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(attendance.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Results count */}
      <div className="text-sm text-gray-500">
        Showing {filteredAttendances.length} of {attendances.length} attendance records
      </div>
    </div>
  )
}
