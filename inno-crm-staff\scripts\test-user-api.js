// Test script for User Management API
// Run with: node scripts/test-user-api.js

const BASE_URL = 'http://localhost:3002'

async function testAPI() {
  console.log('🧪 Testing User Management API...\n')

  // Test 1: Get all users
  console.log('1. Testing GET /api/users')
  try {
    const response = await fetch(`${BASE_URL}/api/users`)
    const data = await response.json()
    console.log('✅ GET users successful')
    console.log(`   Found ${data.users?.length || 0} users`)
    console.log(`   Pagination: ${JSON.stringify(data.pagination)}`)
  } catch (error) {
    console.log('❌ GET users failed:', error.message)
  }

  // Test 2: Create a test user
  console.log('\n2. Testing POST /api/users (Create Cashier)')
  const testUser = {
    name: 'Test Cashier User',
    phone: '+998901234567',
    email: '<EMAIL>',
    role: 'CASHIER',
    password: 'testpassword123'
  }

  try {
    const response = await fetch(`${BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ POST user successful')
      console.log(`   Created user: ${data.name} (${data.role})`)
      console.log(`   User ID: ${data.id}`)
      
      // Test 3: Update the user
      console.log('\n3. Testing PUT /api/users (Update user)')
      const updateData = {
        id: data.id,
        name: 'Updated Cashier User',
        email: '<EMAIL>'
      }
      
      const updateResponse = await fetch(`${BASE_URL}/api/users`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      })
      
      if (updateResponse.ok) {
        const updatedData = await updateResponse.json()
        console.log('✅ PUT user successful')
        console.log(`   Updated name: ${updatedData.name}`)
        console.log(`   Updated email: ${updatedData.email}`)
      } else {
        const error = await updateResponse.json()
        console.log('❌ PUT user failed:', error.error)
      }
      
      // Test 4: Delete the user
      console.log('\n4. Testing DELETE /api/users')
      const deleteResponse = await fetch(`${BASE_URL}/api/users?id=${data.id}`, {
        method: 'DELETE'
      })
      
      if (deleteResponse.ok) {
        const deleteData = await deleteResponse.json()
        console.log('✅ DELETE user successful')
        console.log(`   Message: ${deleteData.message}`)
      } else {
        const error = await deleteResponse.json()
        console.log('❌ DELETE user failed:', error.error)
      }
      
    } else {
      const error = await response.json()
      console.log('❌ POST user failed:', error.error)
    }
  } catch (error) {
    console.log('❌ POST user failed:', error.message)
  }

  // Test 5: Test validation errors
  console.log('\n5. Testing validation (invalid data)')
  const invalidUser = {
    name: 'A', // Too short
    phone: '123', // Too short
    email: 'invalid-email', // Invalid format
    role: 'INVALID_ROLE', // Invalid role
    password: '123' // Too short
  }

  try {
    const response = await fetch(`${BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidUser)
    })
    
    if (!response.ok) {
      const error = await response.json()
      console.log('✅ Validation working correctly')
      console.log(`   Error: ${error.error}`)
      if (error.details) {
        console.log(`   Details: ${JSON.stringify(error.details, null, 2)}`)
      }
    } else {
      console.log('❌ Validation should have failed but didn\'t')
    }
  } catch (error) {
    console.log('❌ Validation test failed:', error.message)
  }

  // Test 6: Test search functionality
  console.log('\n6. Testing search functionality')
  try {
    const response = await fetch(`${BASE_URL}/api/users?search=admin`)
    const data = await response.json()
    console.log('✅ Search functionality working')
    console.log(`   Found ${data.users?.length || 0} users matching "admin"`)
  } catch (error) {
    console.log('❌ Search test failed:', error.message)
  }

  // Test 7: Test role filtering
  console.log('\n7. Testing role filtering')
  try {
    const response = await fetch(`${BASE_URL}/api/users?role=ADMIN`)
    const data = await response.json()
    console.log('✅ Role filtering working')
    console.log(`   Found ${data.users?.length || 0} ADMIN users`)
  } catch (error) {
    console.log('❌ Role filtering test failed:', error.message)
  }

  console.log('\n🎉 API testing completed!')
}

// Run the tests
testAPI().catch(console.error)
