import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import { z } from 'zod'

const updateAssessmentSchema = z.object({
  testName: z.string().optional(),
  type: z.enum(['LEVEL_TEST', 'PROGRESS_TEST', 'FINAL_EXAM', 'GROUP_TEST']).optional(),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']).optional(),
  score: z.number().optional(),
  maxScore: z.number().optional(),
  passed: z.boolean().optional(),
  questions: z.any().optional(),
  results: z.any().optional(),
  completedAt: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    let assessment
    try {
      assessment = await prisma.assessment.findUnique({
        where: { id },
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          // Temporarily comment out group relation
          // group: {
          //   include: {
          //     course: {
          //       select: {
          //         name: true,
          //         level: true,
          //       },
          //     },
          //   },
          // },
        },
      })
    } catch (error) {
      // Fallback without group relation
      assessment = await prisma.assessment.findUnique({
        where: { id },
        include: {
          student: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      })
    }

    if (!assessment) {
      return NextResponse.json({ error: 'Assessment not found' }, { status: 404 })
    }

    return NextResponse.json(assessment)
  } catch (error) {
    console.error('Error fetching assessment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers, admins, and managers can update assessments
    if (!session.user.role || !['ADMIN', 'MANAGER', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateAssessmentSchema.parse(body)

    // Check if assessment exists
    const existingAssessment = await prisma.assessment.findUnique({
      where: { id },
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    })

    if (!existingAssessment) {
      return NextResponse.json({ error: 'Assessment not found' }, { status: 404 })
    }

    const updateData: any = { ...validatedData }
    if (validatedData.completedAt) {
      updateData.completedAt = new Date(validatedData.completedAt)
    }

    const assessment = await prisma.assessment.update({
      where: { id },
      data: updateData,
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: session.user.role as Role,
      action: 'UPDATE',
      resource: 'assessment',
      resourceId: assessment.id,
      details: {
        changes: validatedData,
        studentName: assessment.student?.user.name || 'Unknown',
      },
      ipAddress: ActivityLogger.getIpAddress(request),
      userAgent: ActivityLogger.getUserAgent(request),
    })

    return NextResponse.json(assessment)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating assessment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only admins can delete assessments
    if (!session.user.role || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params

    // Check if assessment exists
    const existingAssessment = await prisma.assessment.findUnique({
      where: { id },
      include: {
        student: {
          include: {
            user: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    })

    if (!existingAssessment) {
      return NextResponse.json({ error: 'Assessment not found' }, { status: 404 })
    }

    await prisma.assessment.delete({
      where: { id },
    })

    // Log the activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: session.user.role as Role,
      action: 'DELETE',
      resource: 'assessment',
      resourceId: id,
      details: {
        studentName: existingAssessment.student?.user.name || 'Unknown',
        type: existingAssessment.type,
      },
      ipAddress: ActivityLogger.getIpAddress(request),
      userAgent: ActivityLogger.getUserAgent(request),
    })

    return NextResponse.json({ message: 'Assessment deleted successfully' })
  } catch (error) {
    console.error('Error deleting assessment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
