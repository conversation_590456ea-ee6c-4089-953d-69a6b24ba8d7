# Database Migration Guide - Comprehensive Leads Management

## Overview

This guide provides step-by-step instructions for migrating your database to support the new Comprehensive Leads Management system.

## Prerequisites

- PostgreSQL database server running
- Database credentials configured in `.env` file
- Node.js and npm installed
- Prisma CLI available

## Migration Steps

### 1. Backup Your Current Database

**IMPORTANT**: Always backup your database before running migrations.

```bash
# For PostgreSQL
pg_dump -U postgres -h localhost inno_crm_dev > backup_$(date +%Y%m%d_%H%M%S).sql

# Or using pgAdmin or your preferred database tool
```

### 2. Update Environment Variables

Ensure your `.env` file has the correct database connection:

```env
DATABASE_URL="postgresql://postgres:password@localhost:5432/inno_crm_dev"
```

### 3. Run the Migration

Choose one of the following methods:

#### Option A: Using Prisma Migrate (Recommended for Production)

```bash
# Generate and apply migration
npx prisma migrate dev --name comprehensive-leads-management

# Generate Prisma client
npx prisma generate
```

#### Option B: Using Prisma DB Push (For Development)

```bash
# Push schema changes directly
npx prisma db push

# Generate Prisma client
npx prisma generate
```

### 4. Seed the Database (Optional)

```bash
# Run the seed script to populate with test data
npm run db:seed
```

## Schema Changes Summary

### New Fields Added to Lead Model

```sql
-- Call management fields
ALTER TABLE leads ADD COLUMN "callStartedAt" TIMESTAMP(3);
ALTER TABLE leads ADD COLUMN "callEndedAt" TIMESTAMP(3);
ALTER TABLE leads ADD COLUMN "callDuration" INTEGER;

-- Group assignment fields
ALTER TABLE leads ADD COLUMN "assignedGroupId" TEXT;
ALTER TABLE leads ADD COLUMN "assignedTeacherId" TEXT;
ALTER TABLE leads ADD COLUMN "assignedAt" TIMESTAMP(3);

-- Archive management
ALTER TABLE leads ADD COLUMN "archivedAt" TIMESTAMP(3);
```

### New CallRecord Table

```sql
CREATE TABLE "call_records" (
    "id" TEXT NOT NULL,
    "leadId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "startedAt" TIMESTAMP(3) NOT NULL,
    "endedAt" TIMESTAMP(3),
    "duration" INTEGER,
    "notes" TEXT,
    "recordingUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "call_records_pkey" PRIMARY KEY ("id")
);
```

### Updated LeadStatus Enum

```sql
-- Drop old enum values and add new ones
ALTER TYPE "LeadStatus" RENAME TO "LeadStatus_old";
CREATE TYPE "LeadStatus" AS ENUM ('NEW', 'CALLING', 'CALL_COMPLETED', 'GROUP_ASSIGNED', 'ARCHIVED', 'NOT_INTERESTED');
ALTER TABLE leads ALTER COLUMN status TYPE "LeadStatus" USING status::text::"LeadStatus";
DROP TYPE "LeadStatus_old";
```

### Foreign Key Constraints

```sql
-- Add foreign key constraints
ALTER TABLE leads ADD CONSTRAINT "leads_assignedGroupId_fkey" 
    FOREIGN KEY ("assignedGroupId") REFERENCES "groups"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE leads ADD CONSTRAINT "leads_assignedTeacherId_fkey" 
    FOREIGN KEY ("assignedTeacherId") REFERENCES "teachers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE call_records ADD CONSTRAINT "call_records_leadId_fkey" 
    FOREIGN KEY ("leadId") REFERENCES "leads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE call_records ADD CONSTRAINT "call_records_userId_fkey" 
    FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
```

## Data Migration (If Needed)

If you have existing leads with old statuses, you may need to migrate them:

```sql
-- Update old statuses to new ones
UPDATE leads SET status = 'CALL_COMPLETED' WHERE status = 'CONTACTED';
UPDATE leads SET status = 'GROUP_ASSIGNED' WHERE status = 'INTERESTED';
UPDATE leads SET status = 'ARCHIVED' WHERE status = 'ENROLLED';
-- NOT_INTERESTED and NEW remain the same
```

## Verification Steps

### 1. Check Schema

```bash
# Verify schema is applied correctly
npx prisma db pull
npx prisma validate
```

### 2. Test Database Connection

```bash
# Open Prisma Studio to verify data
npx prisma studio
```

### 3. Run Application

```bash
# Start the development server
npm run dev
```

### 4. Test New Features

1. Navigate to `/dashboard/leads`
2. Verify the new interface loads
3. Test creating a new lead
4. Test the call management workflow
5. Test group assignment functionality
6. Verify archive functionality

## Troubleshooting

### Common Issues

#### 1. Database Connection Error

```
Error: P1000: Authentication failed against database server
```

**Solution**: Check your database credentials in `.env` file and ensure PostgreSQL is running.

#### 2. Migration Conflicts

```
Error: P3006: Migration conflicts detected
```

**Solution**: Reset the migration state or manually resolve conflicts:

```bash
npx prisma migrate reset
npx prisma migrate dev
```

#### 3. Enum Type Conflicts

```
Error: P3009: migrate found failed migration
```

**Solution**: Drop and recreate the enum types manually or reset migrations.

### Manual Migration (If Automated Migration Fails)

If the automated migration fails, you can run the SQL commands manually:

1. Connect to your PostgreSQL database
2. Run the SQL commands from the "Schema Changes Summary" section above
3. Update any existing data as needed
4. Run `npx prisma generate` to update the Prisma client

## Rollback Plan

If you need to rollback the migration:

### 1. Restore from Backup

```bash
# Restore from your backup
psql -U postgres -h localhost -d inno_crm_dev < backup_YYYYMMDD_HHMMSS.sql
```

### 2. Revert Schema Changes

```bash
# Reset to previous migration
npx prisma migrate reset
# Apply only the migrations you want to keep
```

## Post-Migration Tasks

### 1. Update Application Code

Ensure all API endpoints and components are updated to use the new schema.

### 2. Test All Functionality

- Test all CRUD operations for leads
- Verify role-based access control
- Test the new call management workflow
- Verify group assignment functionality
- Test archive and cleanup features

### 3. Update Documentation

Update any internal documentation to reflect the new workflow.

### 4. Train Users

Provide training to staff on the new leads management workflow.

## Performance Considerations

### Indexes

The migration includes appropriate indexes for performance:

```sql
-- These are automatically created by Prisma
CREATE INDEX "leads_status_idx" ON "leads"("status");
CREATE INDEX "leads_createdAt_idx" ON "leads"("createdAt");
CREATE INDEX "leads_archivedAt_idx" ON "leads"("archivedAt");
CREATE INDEX "call_records_leadId_idx" ON "call_records"("leadId");
```

### Cleanup Job

Set up a cron job for automatic cleanup of archived leads:

```bash
# Add to crontab (runs daily at 2 AM)
0 2 * * * curl -X POST http://localhost:3000/api/leads/cleanup
```

## Support

If you encounter issues during migration:

1. Check the application logs
2. Verify database connectivity
3. Review the Prisma schema validation
4. Consult the troubleshooting section above
5. Contact the development team for assistance

## Success Criteria

Migration is successful when:

- ✅ All schema changes are applied
- ✅ Application starts without errors
- ✅ New leads management interface loads
- ✅ All CRUD operations work correctly
- ✅ Role-based access control functions properly
- ✅ No data loss occurred
- ✅ Performance is acceptable

---

**Migration Status**: Ready for execution
**Estimated Downtime**: 5-10 minutes
**Risk Level**: Low (with proper backup)
