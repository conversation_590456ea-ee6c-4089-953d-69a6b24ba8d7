# Complete Vercel Deployment Guide for Inno-CRM

This guide covers deploying both the **Admin Server** and **Staff Server** to Vercel.

## 🏗️ Architecture Overview

- **Admin Server**: Handles ADMIN and CASHIER roles with full system access
- **Staff Server**: Handles RECEPTION, ACADEMIC_MANAGER, TEACHER, MANAGER, and STUDENT roles
- **Communication**: Secure API communication between servers

## 📋 Prerequisites

1. Two separate GitHub repositories:
   - `inno-crm-admin` (Admin server)
   - `inno-crm-staff` (Staff server - current repository)
2. Vercel account
3. Database access for both servers

## 🚀 Deployment Steps

### Step 1: Prepare Staff Server (Current Repository)

The current repository is already configured as the staff server. 

**Environment Variables for Staff Server:**
```env
# Database Configuration - Staff Database
DATABASE_URL="postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require&channel_binding=require"

# NextAuth.js Configuration
NEXTAUTH_SECRET="inno-crm-staff-super-secret-key-for-production-2024-very-long-and-secure"
NEXTAUTH_URL="https://inno-crm-staff.vercel.app"

# Prisma Configuration
PRISMA_GENERATE_DATAPROXY="true"

# Application Configuration
APP_NAME="Innovative Centre - Staff Portal"
APP_URL="https://inno-crm-staff.vercel.app"
APP_ENV="production"
SERVER_TYPE="staff"

# Inter-Server Communication
ADMIN_SERVER_URL="https://inno-crm-admin.vercel.app"
STAFF_SERVER_URL="https://inno-crm-staff.vercel.app"
INTER_SERVER_SECRET="inno-crm-inter-server-communication-secret-2024"
```

### Step 2: Deploy Staff Server to Vercel

1. **Connect to Vercel:**
   - Go to [vercel.com/new](https://vercel.com/new)
   - Import this repository
   - Project name: `inno-crm-staff`

2. **Configure Environment Variables:**
   - Copy all variables from `.env.production`
   - Update `NEXTAUTH_URL` to match your Vercel domain
   - Add all SMS, email, and other service configurations

3. **Deploy:**
   - Click "Deploy"
   - Vercel will use the `vercel.json` configuration automatically

### Step 3: Prepare Admin Server

You'll need to create a separate repository for the admin server:

1. **Create Admin Repository:**
   ```bash
   # Clone current repository to create admin version
   git clone https://github.com/MrFarrukhT/inno-crm.git inno-crm-admin
   cd inno-crm-admin
   ```

2. **Modify for Admin Server:**
   - Update `package.json` name to `"inno-crm-admin"`
   - Update dev script to `"dev": "next dev"` (port 3000)
   - Copy `vercel.admin.json` to `vercel.json`
   - Use `.env.production.admin` for environment variables

3. **Database Setup:**
   - Create a separate admin database
   - Update DATABASE_URL in admin environment

### Step 4: Deploy Admin Server

1. **Push to GitHub:**
   ```bash
   git remote set-url origin https://github.com/MrFarrukhT/inno-crm-admin.git
   git push origin main
   ```

2. **Deploy to Vercel:**
   - Import the admin repository
   - Project name: `inno-crm-admin`
   - Use environment variables from `.env.production.admin`

## 🔧 Configuration Details

### Staff Server Features (Enabled)
- Lead management
- Student management
- Course and group management
- Attendance tracking
- Assessment management
- Communication tools
- Basic reporting

### Staff Server Features (Disabled)
- Financial analytics
- Payment management
- Admin user management
- Advanced KPI tracking
- System administration

### Admin Server Features (All Enabled)
- All staff server features
- Financial management
- Advanced analytics
- Payment processing
- System administration
- User management
- KPI tracking

## 🔐 Security Configuration

### Inter-Server Communication
- CORS configured for specific domains
- Shared secret for API authentication
- Role-based access control

### Admin Server Security
- IP whitelist capability
- Enhanced security headers
- Restricted access to admin functions

## 🧪 Testing Deployment

### Staff Server Tests
1. Visit `https://inno-crm-staff.vercel.app`
2. Test login with reception/teacher accounts
3. Verify limited feature access
4. Check API endpoints work

### Admin Server Tests
1. Visit `https://inno-crm-admin.vercel.app`
2. Test login with admin/cashier accounts
3. Verify full feature access
4. Test inter-server communication

## 📊 Monitoring

### Health Checks
- Staff: `https://inno-crm-staff.vercel.app/api/health`
- Admin: `https://inno-crm-admin.vercel.app/api/health`

### Logs
- Monitor Vercel function logs
- Check database connection status
- Monitor inter-server API calls

## 🚨 Troubleshooting

### Common Issues
1. **Database Connection**: Verify DATABASE_URL format
2. **CORS Errors**: Check domain configuration in vercel.json
3. **Build Failures**: Ensure Prisma generation works
4. **Authentication**: Verify NEXTAUTH_URL matches deployment URL

### Support
- Check Vercel deployment logs
- Verify environment variables
- Test database connectivity
- Monitor API response times

## 📝 Post-Deployment Checklist

- [ ] Staff server deployed and accessible
- [ ] Admin server deployed and accessible
- [ ] Environment variables configured
- [ ] Database connections working
- [ ] Authentication working on both servers
- [ ] Inter-server communication tested
- [ ] Role-based access verified
- [ ] Health checks passing
- [ ] Monitoring setup complete

## 🔄 Updates and Maintenance

### Updating Staff Server
1. Push changes to staff repository
2. Vercel auto-deploys from main branch
3. Monitor deployment status

### Updating Admin Server
1. Push changes to admin repository
2. Vercel auto-deploys from main branch
3. Test inter-server compatibility

### Database Migrations
1. Test migrations in development
2. Apply to staff database first
3. Apply to admin database
4. Verify both servers work correctly
