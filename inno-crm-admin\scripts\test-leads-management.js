/**
 * Test script for the Comprehensive Leads Management System
 * 
 * This script tests the new leads management workflow:
 * 1. Create a test lead
 * 2. Start a call
 * 3. End the call
 * 4. Get available groups
 * 5. Assign to a group
 * 6. Archive the lead
 * 7. Test cleanup functionality
 */

const BASE_URL = 'http://localhost:3000'

// Test data
const testLead = {
  name: 'Test Lead',
  phone: '+998901234567',
  coursePreference: 'English Language'
}

const testUser = {
  phone: '+998901234568', // Admin user for testing
  password: 'admin123'
}

let authCookie = ''
let leadId = ''
let groupId = ''

async function makeRequest(url, options = {}) {
  const response = await fetch(`${BASE_URL}${url}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': authCookie,
      ...options.headers
    }
  })
  
  const data = await response.json()
  
  if (!response.ok) {
    throw new Error(`${response.status}: ${data.error || 'Request failed'}`)
  }
  
  return data
}

async function login() {
  console.log('🔐 Logging in...')
  
  const response = await fetch(`${BASE_URL}/api/auth/signin`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(testUser)
  })
  
  if (response.ok) {
    authCookie = response.headers.get('set-cookie') || ''
    console.log('✅ Login successful')
  } else {
    console.log('⚠️  Login failed, continuing without auth (for public endpoints)')
  }
}

async function createTestLead() {
  console.log('📝 Creating test lead...')
  
  try {
    const lead = await makeRequest('/api/leads', {
      method: 'POST',
      body: JSON.stringify(testLead)
    })
    
    leadId = lead.id
    console.log(`✅ Lead created with ID: ${leadId}`)
    return lead
  } catch (error) {
    console.error('❌ Failed to create lead:', error.message)
    throw error
  }
}

async function testCallWorkflow() {
  console.log('📞 Testing call workflow...')
  
  try {
    // Start call
    console.log('  Starting call...')
    const startCall = await makeRequest(`/api/leads/${leadId}/call`, {
      method: 'POST',
      body: JSON.stringify({ notes: 'Test call started' })
    })
    console.log('  ✅ Call started')
    
    // Simulate call duration
    console.log('  Simulating 30 second call...')
    await new Promise(resolve => setTimeout(resolve, 2000)) // 2 seconds for testing
    
    // End call
    console.log('  Ending call...')
    const endCall = await makeRequest(`/api/leads/${leadId}/call`, {
      method: 'PUT',
      body: JSON.stringify({ 
        duration: 30,
        notes: 'Test call completed successfully'
      })
    })
    console.log('  ✅ Call ended')
    
    return { startCall, endCall }
  } catch (error) {
    console.error('❌ Call workflow failed:', error.message)
    throw error
  }
}

async function testGroupAssignment() {
  console.log('👥 Testing group assignment...')
  
  try {
    // Get available groups
    console.log('  Fetching available groups...')
    const groupsResponse = await makeRequest(`/api/leads/${leadId}/assign-group`)
    const groups = groupsResponse.groups
    
    if (groups.length === 0) {
      console.log('  ⚠️  No groups available for assignment')
      return null
    }
    
    groupId = groups[0].id
    console.log(`  Found ${groups.length} available groups`)
    
    // Assign to first available group
    console.log('  Assigning to group...')
    const assignment = await makeRequest(`/api/leads/${leadId}/assign-group`, {
      method: 'POST',
      body: JSON.stringify({
        groupId: groupId,
        notes: 'Test group assignment'
      })
    })
    console.log('  ✅ Group assigned successfully')
    
    return assignment
  } catch (error) {
    console.error('❌ Group assignment failed:', error.message)
    throw error
  }
}

async function testArchive() {
  console.log('📁 Testing archive functionality...')
  
  try {
    const archive = await makeRequest(`/api/leads/${leadId}/archive`, {
      method: 'POST'
    })
    console.log('✅ Lead archived successfully')
    return archive
  } catch (error) {
    console.error('❌ Archive failed:', error.message)
    throw error
  }
}

async function testLeadsAPI() {
  console.log('📋 Testing leads API with filters...')
  
  try {
    // Test basic fetch
    const allLeads = await makeRequest('/api/leads')
    console.log(`  Found ${allLeads.leads.length} total leads`)
    
    // Test status filter
    const newLeads = await makeRequest('/api/leads?status=NEW')
    console.log(`  Found ${newLeads.leads.length} NEW leads`)
    
    // Test date filter
    const todayLeads = await makeRequest('/api/leads?dateFilter=today')
    console.log(`  Found ${todayLeads.leads.length} leads from today`)
    
    // Test archived leads
    const archivedLeads = await makeRequest('/api/leads?archived=true')
    console.log(`  Found ${archivedLeads.leads.length} archived leads`)
    
    console.log('✅ Leads API tests passed')
  } catch (error) {
    console.error('❌ Leads API test failed:', error.message)
    throw error
  }
}

async function testCleanup() {
  console.log('🧹 Testing cleanup functionality...')
  
  try {
    // Get cleanup preview
    const preview = await makeRequest('/api/leads/cleanup')
    console.log(`  Preview: ${preview.leadsToDelete} leads eligible for cleanup`)
    
    // Note: We won't actually run cleanup in tests to preserve data
    console.log('✅ Cleanup preview successful')
    return preview
  } catch (error) {
    console.error('❌ Cleanup test failed:', error.message)
    // Don't throw here as cleanup might fail due to permissions
  }
}

async function runTests() {
  console.log('🚀 Starting Comprehensive Leads Management System Tests\n')
  
  try {
    await login()
    
    const lead = await createTestLead()
    console.log('')
    
    const callResult = await testCallWorkflow()
    console.log('')
    
    const groupAssignment = await testGroupAssignment()
    console.log('')
    
    if (groupAssignment) {
      await testArchive()
      console.log('')
    }
    
    await testLeadsAPI()
    console.log('')
    
    await testCleanup()
    console.log('')
    
    console.log('🎉 All tests completed successfully!')
    console.log('\n📊 Test Summary:')
    console.log(`   Lead ID: ${leadId}`)
    console.log(`   Group ID: ${groupId || 'N/A'}`)
    console.log(`   Status: ${groupAssignment ? 'ARCHIVED' : 'CALL_COMPLETED'}`)
    
  } catch (error) {
    console.error('\n💥 Test failed:', error.message)
    process.exit(1)
  }
}

// Handle command line arguments
const args = process.argv.slice(2)
if (args.includes('--help')) {
  console.log(`
Usage: node scripts/test-leads-management.js [options]

Options:
  --help     Show this help message

Environment:
  Make sure the Next.js server is running on http://localhost:3000
  Ensure the database is set up and contains test data

Test Flow:
  1. Create a test lead
  2. Start and end a call
  3. Assign to a group (if available)
  4. Archive the lead
  5. Test API filtering
  6. Test cleanup preview
`)
  process.exit(0)
}

// Run the tests
runTests().catch(console.error)
