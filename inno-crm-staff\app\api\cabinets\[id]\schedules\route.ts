import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const scheduleSchema = z.object({
  groupId: z.string().optional(),
  dayOfWeek: z.number().min(0).max(6), // 0 = Sunday, 6 = Saturday
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  title: z.string().optional(),
  isBlocked: z.boolean().default(false),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if cabinet exists
    const cabinet = await prisma.cabinet.findUnique({
      where: { id },
    })

    if (!cabinet) {
      return NextResponse.json(
        { error: 'Cabinet not found' },
        { status: 404 }
      )
    }

    const schedules = await prisma.cabinetSchedule.findMany({
      where: { cabinetId: id },
      include: {
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: [
        { dayOfWeek: 'asc' },
        { startTime: 'asc' },
      ],
    })

    return NextResponse.json(schedules)
  } catch (error) {
    console.error('Error fetching cabinet schedules:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to manage schedules
    if (!session.user.role || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = scheduleSchema.parse(body)

    // Check if cabinet exists
    const cabinet = await prisma.cabinet.findUnique({
      where: { id },
    })

    if (!cabinet) {
      return NextResponse.json(
        { error: 'Cabinet not found' },
        { status: 404 }
      )
    }

    // Validate time range
    if (validatedData.startTime >= validatedData.endTime) {
      return NextResponse.json(
        { error: 'End time must be after start time' },
        { status: 400 }
      )
    }

    // Check for time conflicts
    const conflictingSchedule = await prisma.cabinetSchedule.findFirst({
      where: {
        cabinetId: id,
        dayOfWeek: validatedData.dayOfWeek,
        OR: [
          {
            AND: [
              { startTime: { lte: validatedData.startTime } },
              { endTime: { gt: validatedData.startTime } },
            ],
          },
          {
            AND: [
              { startTime: { lt: validatedData.endTime } },
              { endTime: { gte: validatedData.endTime } },
            ],
          },
          {
            AND: [
              { startTime: { gte: validatedData.startTime } },
              { endTime: { lte: validatedData.endTime } },
            ],
          },
        ],
      },
    })

    if (conflictingSchedule) {
      return NextResponse.json(
        { error: 'Time slot conflicts with existing schedule' },
        { status: 400 }
      )
    }

    // Validate group if provided
    if (validatedData.groupId) {
      const group = await prisma.group.findUnique({
        where: { id: validatedData.groupId },
      })

      if (!group) {
        return NextResponse.json(
          { error: 'Group not found' },
          { status: 400 }
        )
      }
    }

    const schedule = await prisma.cabinetSchedule.create({
      data: {
        cabinetId: id,
        ...validatedData,
      },
      include: {
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
            teacher: {
              include: {
                user: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    // Log activity
    await ActivityLogger.log({
      userId: session.user.id,
      userRole: (session.user.role as Role) || Role.ADMIN,
      action: 'CREATE',
      resource: 'CABINET_SCHEDULE',
      resourceId: schedule.id,
      details: `Created schedule for cabinet ${cabinet.name} on ${getDayName(validatedData.dayOfWeek)} ${validatedData.startTime}-${validatedData.endTime}`,
    })

    return NextResponse.json(schedule, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating cabinet schedule:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getDayName(dayOfWeek: number): string {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  return days[dayOfWeek] || 'Unknown'
}
