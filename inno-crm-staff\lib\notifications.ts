// Unified notification system combining SMS and Email
import { createSMSService, SMS_TEMPLATES, SMSMessage } from './sms'
import { createEmailService, EMAIL_TEMPLATES, EmailMessage } from './email'
import { prisma } from './prisma'

interface NotificationPreferences {
  sms: boolean
  email: boolean
  push: boolean
}

interface NotificationRecipient {
  id: string
  name: string
  phone?: string
  email?: string
  preferences?: NotificationPreferences
}

interface NotificationOptions {
  type: 'enrollment' | 'payment' | 'reminder' | 'completion' | 'attendance'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  channels?: ('sms' | 'email' | 'push')[]
  data: Record<string, any>
}

interface NotificationResult {
  success: boolean
  sms?: { success: boolean; messageId?: string; error?: string }
  email?: { success: boolean; messageId?: string; error?: string }
  push?: { success: boolean; messageId?: string; error?: string }
}

class NotificationService {
  private smsService = createSMSService()
  private emailService = createEmailService()

  async sendNotification(
    recipient: NotificationRecipient,
    options: NotificationOptions
  ): Promise<NotificationResult> {
    const result: NotificationResult = { success: false }
    
    // Determine which channels to use
    const channels = options.channels || this.getDefaultChannels(options.type, options.priority)
    const userPreferences = recipient.preferences || { sms: true, email: true, push: false }

    // Send SMS if enabled and recipient has phone
    if (channels.includes('sms') && userPreferences.sms && recipient.phone) {
      try {
        const smsMessage = this.generateSMSMessage(options.type, options.data)
        result.sms = await this.smsService.sendSMS({
          to: recipient.phone,
          message: smsMessage,
        })
      } catch (error) {
        result.sms = {
          success: false,
          error: error instanceof Error ? error.message : 'SMS failed'
        }
      }
    }

    // Send Email if enabled and recipient has email
    if (channels.includes('email') && userPreferences.email && recipient.email) {
      try {
        const emailTemplate = this.generateEmailMessage(options.type, options.data)
        result.email = await this.emailService.sendEmail({
          to: recipient.email,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
        })
      } catch (error) {
        result.email = {
          success: false,
          error: error instanceof Error ? error.message : 'Email failed'
        }
      }
    }

    // Log notification attempt
    await this.logNotification(recipient.id, options, result)

    // Determine overall success
    result.success = (result.sms?.success || result.email?.success) || false

    return result
  }

  async sendBulkNotification(
    recipients: NotificationRecipient[],
    options: NotificationOptions
  ): Promise<NotificationResult[]> {
    const results = await Promise.all(
      recipients.map(recipient => this.sendNotification(recipient, options))
    )

    // Log bulk notification summary
    const successCount = results.filter(r => r.success).length
    console.log(`Bulk notification sent: ${successCount}/${recipients.length} successful`)

    return results
  }

  private getDefaultChannels(type: string, priority: string): ('sms' | 'email' | 'push')[] {
    switch (priority) {
      case 'urgent':
        return ['sms', 'email', 'push']
      case 'high':
        return ['sms', 'email']
      case 'medium':
        return ['email', 'sms']
      case 'low':
        return ['email']
      default:
        return ['email']
    }
  }

  private generateSMSMessage(type: string, data: any): string {
    switch (type) {
      case 'enrollment':
        return SMS_TEMPLATES.ENROLLMENT_CONFIRMATION(
          data.studentName,
          data.courseName,
          data.startDate
        )
      case 'payment':
        if (data.isConfirmation) {
          return SMS_TEMPLATES.PAYMENT_CONFIRMATION(
            data.studentName,
            data.amount,
            data.courseName
          )
        } else {
          return SMS_TEMPLATES.PAYMENT_REMINDER(
            data.studentName,
            data.amount,
            data.dueDate
          )
        }
      case 'reminder':
        return SMS_TEMPLATES.CLASS_REMINDER(
          data.studentName,
          data.courseName,
          data.time
        )
      case 'completion':
        return SMS_TEMPLATES.COURSE_COMPLETION(
          data.studentName,
          data.courseName,
          data.nextLevel
        )
      case 'attendance':
        return SMS_TEMPLATES.ATTENDANCE_ALERT(
          data.parentName,
          data.studentName,
          data.courseName
        )
      default:
        return data.message || 'Notification from Innovative Centre'
    }
  }

  private generateEmailMessage(type: string, data: any): { subject: string; html: string } {
    switch (type) {
      case 'enrollment':
        return EMAIL_TEMPLATES.ENROLLMENT_CONFIRMATION(
          data.studentName,
          data.courseName,
          data.startDate,
          data.groupName
        )
      case 'payment':
        if (data.isConfirmation) {
          return EMAIL_TEMPLATES.PAYMENT_CONFIRMATION(
            data.studentName,
            data.amount,
            data.courseName,
            data.paymentMethod,
            data.transactionId
          )
        } else {
          return EMAIL_TEMPLATES.PAYMENT_REMINDER(
            data.studentName,
            data.amount,
            data.dueDate,
            data.courseName
          )
        }
      case 'completion':
        return EMAIL_TEMPLATES.COURSE_COMPLETION(
          data.studentName,
          data.courseName,
          data.completionDate,
          data.nextLevel
        )
      default:
        return {
          subject: data.subject || 'Notification from Innovative Centre',
          html: data.html || data.message || 'You have a new notification.'
        }
    }
  }

  private async logNotification(
    recipientId: string,
    options: NotificationOptions,
    result: NotificationResult
  ): Promise<void> {
    try {
      // In a real implementation, you might want to create a notifications table
      // For now, we'll just log to console
      console.log('Notification Log:', {
        recipientId,
        type: options.type,
        priority: options.priority,
        success: result.success,
        timestamp: new Date().toISOString(),
        channels: {
          sms: result.sms?.success || false,
          email: result.email?.success || false,
        }
      })
    } catch (error) {
      console.error('Failed to log notification:', error)
    }
  }

  // Convenience methods for common notification types
  async sendEnrollmentConfirmation(
    student: { name: string; phone?: string; email?: string },
    course: { name: string; startDate: string },
    group: { name: string }
  ): Promise<NotificationResult> {
    return this.sendNotification(
      {
        id: 'student',
        name: student.name,
        phone: student.phone,
        email: student.email,
      },
      {
        type: 'enrollment',
        priority: 'high',
        data: {
          studentName: student.name,
          courseName: course.name,
          startDate: course.startDate,
          groupName: group.name,
        },
      }
    )
  }

  async sendPaymentConfirmation(
    student: { name: string; phone?: string; email?: string },
    payment: { amount: number; method: string; transactionId?: string },
    course: { name: string }
  ): Promise<NotificationResult> {
    return this.sendNotification(
      {
        id: 'student',
        name: student.name,
        phone: student.phone,
        email: student.email,
      },
      {
        type: 'payment',
        priority: 'medium',
        data: {
          studentName: student.name,
          amount: payment.amount,
          courseName: course.name,
          paymentMethod: payment.method,
          transactionId: payment.transactionId,
          isConfirmation: true,
        },
      }
    )
  }

  async sendPaymentReminder(
    student: { name: string; phone?: string; email?: string },
    payment: { amount: number; dueDate: string },
    course: { name: string }
  ): Promise<NotificationResult> {
    return this.sendNotification(
      {
        id: 'student',
        name: student.name,
        phone: student.phone,
        email: student.email,
      },
      {
        type: 'payment',
        priority: 'high',
        data: {
          studentName: student.name,
          amount: payment.amount,
          dueDate: payment.dueDate,
          courseName: course.name,
          isConfirmation: false,
        },
      }
    )
  }

  async sendClassReminder(
    students: Array<{ name: string; phone?: string; email?: string }>,
    classInfo: { courseName: string; time: string }
  ): Promise<NotificationResult[]> {
    const recipients = students.map((student, index) => ({
      id: `student-${index}`,
      name: student.name,
      phone: student.phone,
      email: student.email,
    }))

    return this.sendBulkNotification(recipients, {
      type: 'reminder',
      priority: 'medium',
      channels: ['sms'], // Class reminders are typically sent via SMS
      data: {
        courseName: classInfo.courseName,
        time: classInfo.time,
      },
    })
  }

  async sendAttendanceAlert(
    parent: { name: string; phone?: string; email?: string },
    student: { name: string },
    course: { name: string }
  ): Promise<NotificationResult> {
    return this.sendNotification(
      {
        id: 'parent',
        name: parent.name,
        phone: parent.phone,
        email: parent.email,
      },
      {
        type: 'attendance',
        priority: 'high',
        channels: ['sms'], // Attendance alerts are urgent and sent via SMS
        data: {
          parentName: parent.name,
          studentName: student.name,
          courseName: course.name,
        },
      }
    )
  }
}

// Singleton instance
let notificationService: NotificationService | null = null

export function getNotificationService(): NotificationService {
  if (!notificationService) {
    notificationService = new NotificationService()
  }
  return notificationService
}

export { NotificationService }
export type { NotificationOptions, NotificationResult, NotificationRecipient }
