import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getWorkflowEngine } from '@/lib/workflows'

const triggerWorkflowSchema = z.object({
  event: z.string(),
  data: z.record(z.any()),
})

const workflowSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  trigger: z.object({
    event: z.string(),
    conditions: z.record(z.any()).optional(),
    delay: z.number().optional(),
  }),
  actions: z.array(z.object({
    type: z.enum(['notification', 'update_status', 'create_record', 'send_reminder']),
    data: z.record(z.any()),
  })),
  enabled: z.boolean(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const workflowEngine = getWorkflowEngine()

    if (action === 'list') {
      // Get all workflows
      const workflows = workflowEngine.getWorkflows()
      return NextResponse.json({ workflows })
    }

    if (action === 'check-overdue-payments') {
      // Manually trigger overdue payment check
      await workflowEngine.checkOverduePayments()
      return NextResponse.json({ 
        success: true, 
        message: 'Overdue payment check completed' 
      })
    }

    if (action === 'check-upcoming-classes') {
      // Manually trigger upcoming classes check
      await workflowEngine.checkUpcomingClasses()
      return NextResponse.json({ 
        success: true, 
        message: 'Upcoming classes check completed' 
      })
    }

    if (action === 'check-payments-due-soon') {
      // Manually trigger payments due soon check
      await workflowEngine.checkPaymentsDueSoon()
      return NextResponse.json({ 
        success: true, 
        message: 'Payments due soon check completed' 
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
  } catch (error) {
    console.error('Error in workflows GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const workflowEngine = getWorkflowEngine()

    if (body.action === 'trigger') {
      // Trigger a workflow manually
      const validatedData = triggerWorkflowSchema.parse(body)
      
      await workflowEngine.triggerWorkflow(validatedData.event, validatedData.data)
      
      return NextResponse.json({
        success: true,
        message: `Workflow triggered for event: ${validatedData.event}`,
      })
    }

    if (body.action === 'add') {
      // Add a new workflow
      const validatedData = workflowSchema.parse(body.workflow)
      
      workflowEngine.addWorkflow(validatedData)
      
      return NextResponse.json({
        success: true,
        message: 'Workflow added successfully',
      })
    }

    if (body.action === 'enable') {
      // Enable a workflow
      const { workflowId } = body
      
      if (!workflowId) {
        return NextResponse.json(
          { error: 'Workflow ID is required' },
          { status: 400 }
        )
      }
      
      workflowEngine.enableWorkflow(workflowId)
      
      return NextResponse.json({
        success: true,
        message: `Workflow ${workflowId} enabled`,
      })
    }

    if (body.action === 'disable') {
      // Disable a workflow
      const { workflowId } = body
      
      if (!workflowId) {
        return NextResponse.json(
          { error: 'Workflow ID is required' },
          { status: 400 }
        )
      }
      
      workflowEngine.disableWorkflow(workflowId)
      
      return NextResponse.json({
        success: true,
        message: `Workflow ${workflowId} disabled`,
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error in workflows POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { workflowId, enabled } = body
    const workflowEngine = getWorkflowEngine()

    if (!workflowId) {
      return NextResponse.json(
        { error: 'Workflow ID is required' },
        { status: 400 }
      )
    }

    if (enabled) {
      workflowEngine.enableWorkflow(workflowId)
    } else {
      workflowEngine.disableWorkflow(workflowId)
    }

    return NextResponse.json({
      success: true,
      message: `Workflow ${workflowId} ${enabled ? 'enabled' : 'disabled'}`,
    })
  } catch (error) {
    console.error('Error updating workflow:', error)
    return NextResponse.json(
      { error: 'Failed to update workflow' },
      { status: 500 }
    )
  }
}
