const fs = require('fs')

console.log('🧪 CRUD Operations Testing Script for inno-crm')
console.log('=' .repeat(50))

// Test endpoints that require full CRUD operations
const crudEndpoints = [
  'students',
  'teachers', 
  'courses',
  'groups',
  'enrollments',
  'payments',
  'attendance',
  'assessments',
  'leads',
  'users',
  'classes'
]

// Test endpoints that only require read operations
const readOnlyEndpoints = [
  'notifications',
  'workflows', 
  'analytics',
  'kpis',
  'reports',
  'activity-logs'
]

const baseUrl = 'http://localhost:3000/api'

// Helper function to make HTTP requests
async function makeRequest(url, method = 'GET', body = null, headers = {}) {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    ...headers
  }

  const options = {
    method,
    headers: defaultHeaders
  }

  if (body && method !== 'GET') {
    options.body = JSON.stringify(body)
  }

  try {
    const response = await fetch(url, options)
    const data = await response.json()
    
    return {
      status: response.status,
      ok: response.ok,
      data
    }
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    }
  }
}

// Test CRUD operations for a specific endpoint
async function testCrudEndpoint(endpoint) {
  console.log(`\n🔍 Testing ${endpoint.toUpperCase()} CRUD operations...`)
  
  const results = {
    create: false,
    read: false,
    readById: false,
    update: false,
    delete: false,
    errors: []
  }

  try {
    // Test READ (GET /)
    console.log(`  📖 Testing READ (GET /${endpoint})...`)
    const readResponse = await makeRequest(`${baseUrl}/${endpoint}`)
    if (readResponse.ok) {
      results.read = true
      console.log(`    ✅ READ operation successful`)
    } else {
      results.errors.push(`READ failed: ${readResponse.status} - ${readResponse.data?.error || 'Unknown error'}`)
      console.log(`    ❌ READ operation failed: ${readResponse.status}`)
    }

    // For endpoints that support full CRUD, test other operations
    if (crudEndpoints.includes(endpoint)) {
      
      // Test CREATE (POST /)
      console.log(`  ➕ Testing CREATE (POST /${endpoint})...`)
      const testData = getTestData(endpoint)
      const createResponse = await makeRequest(`${baseUrl}/${endpoint}`, 'POST', testData)
      
      if (createResponse.ok) {
        results.create = true
        console.log(`    ✅ CREATE operation successful`)
        
        const createdId = createResponse.data?.id
        
        if (createdId) {
          // Test READ BY ID (GET /:id)
          console.log(`  🔍 Testing READ BY ID (GET /${endpoint}/${createdId})...`)
          const readByIdResponse = await makeRequest(`${baseUrl}/${endpoint}/${createdId}`)
          
          if (readByIdResponse.ok) {
            results.readById = true
            console.log(`    ✅ READ BY ID operation successful`)
          } else {
            results.errors.push(`READ BY ID failed: ${readByIdResponse.status}`)
            console.log(`    ❌ READ BY ID operation failed: ${readByIdResponse.status}`)
          }

          // Test UPDATE (PUT /:id)
          console.log(`  ✏️  Testing UPDATE (PUT /${endpoint}/${createdId})...`)
          const updateData = getUpdateTestData(endpoint)
          const updateResponse = await makeRequest(`${baseUrl}/${endpoint}/${createdId}`, 'PUT', updateData)
          
          if (updateResponse.ok) {
            results.update = true
            console.log(`    ✅ UPDATE operation successful`)
          } else {
            results.errors.push(`UPDATE failed: ${updateResponse.status} - ${updateResponse.data?.error || 'Unknown error'}`)
            console.log(`    ❌ UPDATE operation failed: ${updateResponse.status}`)
          }

          // Test DELETE (DELETE /:id)
          console.log(`  🗑️  Testing DELETE (DELETE /${endpoint}/${createdId})...`)
          const deleteResponse = await makeRequest(`${baseUrl}/${endpoint}/${createdId}`, 'DELETE')
          
          if (deleteResponse.ok) {
            results.delete = true
            console.log(`    ✅ DELETE operation successful`)
          } else {
            results.errors.push(`DELETE failed: ${deleteResponse.status} - ${deleteResponse.data?.error || 'Unknown error'}`)
            console.log(`    ❌ DELETE operation failed: ${deleteResponse.status}`)
          }
        } else {
          results.errors.push('CREATE response did not include ID')
        }
      } else {
        results.errors.push(`CREATE failed: ${createResponse.status} - ${createResponse.data?.error || 'Unknown error'}`)
        console.log(`    ❌ CREATE operation failed: ${createResponse.status}`)
      }
    }

  } catch (error) {
    results.errors.push(`Test error: ${error.message}`)
    console.log(`    ❌ Test error: ${error.message}`)
  }

  return results
}

// Get test data for creating records
function getTestData(endpoint) {
  const testData = {
    students: {
      name: 'Test Student',
      phone: '+998901234567',
      email: '<EMAIL>',
      level: 'A1',
      branch: 'Main'
    },
    teachers: {
      name: 'Test Teacher',
      phone: '+998901234568',
      email: '<EMAIL>',
      subject: 'English',
      experience: 5,
      salary: 1000000,
      branch: 'Main'
    },
    courses: {
      name: 'Test Course',
      level: 'A1',
      description: 'Test course description',
      duration: 12,
      price: 500000
    },
    users: {
      name: 'Test User',
      phone: '+998901234569',
      email: '<EMAIL>',
      role: 'STUDENT',
      password: 'password123'
    }
  }

  return testData[endpoint] || { name: 'Test Item' }
}

// Get test data for updating records
function getUpdateTestData(endpoint) {
  const updateData = {
    students: { name: 'Updated Student Name' },
    teachers: { experience: 6 },
    courses: { description: 'Updated course description' },
    users: { name: 'Updated User Name' }
  }

  return updateData[endpoint] || { name: 'Updated Item' }
}

// Main test function
async function runTests() {
  console.log('\n🚀 Starting CRUD Operations Tests...')
  console.log('Note: This requires the development server to be running on localhost:3000')
  
  const allResults = {}
  
  // Test CRUD endpoints
  for (const endpoint of crudEndpoints) {
    allResults[endpoint] = await testCrudEndpoint(endpoint)
    await new Promise(resolve => setTimeout(resolve, 100)) // Small delay between tests
  }
  
  // Test read-only endpoints
  for (const endpoint of readOnlyEndpoints) {
    console.log(`\n🔍 Testing ${endpoint.toUpperCase()} READ operation...`)
    const readResponse = await makeRequest(`${baseUrl}/${endpoint}`)
    allResults[endpoint] = {
      read: readResponse.ok,
      errors: readResponse.ok ? [] : [`READ failed: ${readResponse.status}`]
    }
    console.log(`  ${readResponse.ok ? '✅' : '❌'} READ operation ${readResponse.ok ? 'successful' : 'failed'}`)
  }

  // Generate summary
  console.log('\n' + '='.repeat(50))
  console.log('📊 TEST RESULTS SUMMARY')
  console.log('='.repeat(50))

  let totalTests = 0
  let passedTests = 0
  let failedEndpoints = []

  for (const [endpoint, results] of Object.entries(allResults)) {
    const operations = Object.keys(results).filter(key => key !== 'errors')
    const passed = operations.filter(op => results[op]).length
    const total = operations.length
    
    totalTests += total
    passedTests += passed
    
    console.log(`\n${endpoint.toUpperCase()}:`)
    console.log(`  ✅ Passed: ${passed}/${total}`)
    
    if (results.errors && results.errors.length > 0) {
      console.log(`  ❌ Errors:`)
      results.errors.forEach(error => console.log(`    - ${error}`))
      failedEndpoints.push(endpoint)
    }
  }

  console.log('\n' + '='.repeat(50))
  console.log(`🎯 OVERALL RESULTS: ${passedTests}/${totalTests} tests passed`)
  console.log(`📈 Success Rate: ${((passedTests/totalTests) * 100).toFixed(1)}%`)
  
  if (failedEndpoints.length > 0) {
    console.log(`\n⚠️  Endpoints with issues: ${failedEndpoints.join(', ')}`)
  } else {
    console.log('\n🎉 All endpoints are working correctly!')
  }

  // Save results to file
  fs.writeFileSync('crud-test-results.json', JSON.stringify(allResults, null, 2))
  console.log('\n📄 Detailed results saved to crud-test-results.json')
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support')
  console.log('Please upgrade Node.js or install node-fetch package')
  process.exit(1)
}

// Run tests
runTests().catch(error => {
  console.error('❌ Test execution failed:', error)
  process.exit(1)
})
