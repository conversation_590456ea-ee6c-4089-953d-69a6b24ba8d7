# Database Setup Instructions

## Option 1: Using Docker (Recommended)

1. Install Docker Desktop if not already installed
2. Run PostgreSQL in Docker:

```bash
docker run --name inno-crm-postgres \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_DB=inno_crm_dev \
  -p 5432:5432 \
  -d postgres:15
```

3. Update your `.env` file:
```
DATABASE_URL="postgresql://postgres:password@localhost:5432/inno_crm_dev"
```

## Option 2: Local PostgreSQL Installation

1. Install PostgreSQL locally
2. Create a database:
```sql
CREATE DATABASE inno_crm_dev;
```

3. Update your `.env` file with your credentials:
```
DATABASE_URL="postgresql://your_username:your_password@localhost:5432/inno_crm_dev"
```

## Apply Database Schema

After setting up PostgreSQL, run:

```bash
# Generate Prisma client
npx prisma generate

# Apply schema changes
npx prisma db push

# Optional: Seed the database
npx prisma db seed
```

## Troubleshooting

If you get authentication errors:
1. Make sure PostgreSQL is running
2. Check your username/password in the DATABASE_URL
3. Ensure the database exists
4. Check if PostgreSQL is listening on port 5432
