"use client"

import { useState, useEffect, useCallback } from "react"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadialBarChart,
  RadialBar,
} from "recharts"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CheckCircle, XCircle, Clock, AlertCircle, Calendar } from "lucide-react"

interface AttendanceData {
  date: string
  present: number
  absent: number
  late: number
  excused: number
  total: number
  rate: number
}

interface GroupAttendanceData {
  group: string
  course: string
  attendanceRate: number
  totalClasses: number
  averageAttendance: number
}

interface AttendanceChartProps {
  className?: string
}

export function AttendanceChart({ className }: AttendanceChartProps) {
  const [attendanceData, setAttendanceData] = useState<AttendanceData[]>([])
  const [groupData, setGroupData] = useState<GroupAttendanceData[]>([])
  const [chartType, setChartType] = useState<"line" | "bar">("line")
  const [timeRange, setTimeRange] = useState("30days")
  const [loading, setLoading] = useState(true)

  // Fetch attendance analytics data
  const fetchAttendanceData = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/analytics/attendance?range=${timeRange}`)
      if (response.ok) {
        const data = await response.json()
        setAttendanceData(data.dailyAttendance || [])
        setGroupData(data.groupAttendance || [])
      }
    } catch (error) {
      console.error("Error fetching attendance data:", error)
      // Mock data for development
      setAttendanceData([
        { date: "2024-01-01", present: 145, absent: 12, late: 8, excused: 3, total: 168, rate: 86.3 },
        { date: "2024-01-02", present: 152, absent: 9, late: 6, excused: 2, total: 169, rate: 89.9 },
        { date: "2024-01-03", present: 148, absent: 15, late: 4, excused: 1, total: 168, rate: 88.1 },
        { date: "2024-01-04", present: 156, absent: 8, late: 3, excused: 2, total: 169, rate: 92.3 },
        { date: "2024-01-05", present: 142, absent: 18, late: 7, excused: 3, total: 170, rate: 83.5 },
        { date: "2024-01-08", present: 159, absent: 6, late: 4, excused: 1, total: 170, rate: 93.5 },
        { date: "2024-01-09", present: 154, absent: 11, late: 5, excused: 2, total: 172, rate: 89.5 },
        { date: "2024-01-10", present: 161, absent: 7, late: 3, excused: 1, total: 172, rate: 93.6 },
        { date: "2024-01-11", present: 147, absent: 16, late: 8, excused: 2, total: 173, rate: 85.0 },
        { date: "2024-01-12", present: 165, absent: 5, late: 2, excused: 1, total: 173, rate: 95.4 },
      ])
      setGroupData([
        { group: "A1-Morning", course: "General English A1", attendanceRate: 92.5, totalClasses: 20, averageAttendance: 18.5 },
        { group: "B1-Evening", course: "General English B1", attendanceRate: 88.3, totalClasses: 18, averageAttendance: 15.9 },
        { group: "IELTS-6.0", course: "IELTS Preparation", attendanceRate: 94.2, totalClasses: 22, averageAttendance: 20.7 },
        { group: "A2-Afternoon", course: "General English A2", attendanceRate: 85.7, totalClasses: 19, averageAttendance: 16.3 },
        { group: "Kids-Saturday", course: "Kids English", attendanceRate: 78.9, totalClasses: 16, averageAttendance: 12.6 },
        { group: "B2-Morning", course: "General English B2", attendanceRate: 91.4, totalClasses: 21, averageAttendance: 19.2 },
      ])
    } finally {
      setLoading(false)
    }
  }, [timeRange])

  useEffect(() => {
    fetchAttendanceData()
  }, [fetchAttendanceData])

  // Calculate overall metrics
  const totalStudents = attendanceData.reduce((sum, day) => Math.max(sum, day.total), 0)
  const averageAttendanceRate = attendanceData.length > 0
    ? (attendanceData.reduce((sum, day) => sum + day.rate, 0) / attendanceData.length).toFixed(1)
    : "0"
  
  const totalPresent = attendanceData.reduce((sum, day) => sum + day.present, 0)
  const totalAbsent = attendanceData.reduce((sum, day) => sum + day.absent, 0)
  const totalLate = attendanceData.reduce((sum, day) => sum + day.late, 0)
  const totalExcused = attendanceData.reduce((sum, day) => sum + day.excused, 0)

  // Attendance status distribution
  const statusData = [
    { name: "Present", value: totalPresent, color: "#00C49F" },
    { name: "Absent", value: totalAbsent, color: "#FF8042" },
    { name: "Late", value: totalLate, color: "#FFBB28" },
    { name: "Excused", value: totalExcused, color: "#8884D8" },
  ]

  // Colors for charts
  const COLORS = ["#00C49F", "#FF8042", "#FFBB28", "#8884D8"]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">Attendance Analytics</h3>
          <p className="text-sm text-gray-500">Monitor student attendance patterns and trends</p>
        </div>
        <div className="flex gap-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="7days">Last 7 Days</option>
            <option value="30days">Last 30 Days</option>
            <option value="90days">Last 90 Days</option>
          </select>
          <Button
            variant={chartType === "line" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("line")}
          >
            Line
          </Button>
          <Button
            variant={chartType === "bar" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("bar")}
          >
            Bar
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Average Attendance</p>
              <p className="text-2xl font-bold text-green-600">{averageAttendanceRate}%</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Students</p>
              <p className="text-2xl font-bold">{totalStudents}</p>
            </div>
            <Calendar className="h-8 w-8 text-blue-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Late Arrivals</p>
              <p className="text-2xl font-bold text-yellow-600">{totalLate}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Absences</p>
              <p className="text-2xl font-bold text-red-600">{totalAbsent}</p>
            </div>
            <XCircle className="h-8 w-8 text-red-600" />
          </div>
        </Card>
      </div>

      {/* Attendance Trend Chart */}
      <Card className="p-6">
        <h4 className="text-lg font-medium mb-4">Daily Attendance Trends</h4>
        {loading ? (
          <div className="h-80 flex items-center justify-center">
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={400}>
            {chartType === "line" ? (
              <LineChart data={attendanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <Legend />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="present"
                  stroke="#00C49F"
                  strokeWidth={3}
                  dot={{ fill: "#00C49F", strokeWidth: 2, r: 4 }}
                  name="Present"
                />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="absent"
                  stroke="#FF8042"
                  strokeWidth={2}
                  dot={{ fill: "#FF8042", strokeWidth: 2, r: 3 }}
                  name="Absent"
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="rate"
                  stroke="#0088FE"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ fill: "#0088FE", strokeWidth: 2, r: 3 }}
                  name="Attendance Rate (%)"
                />
              </LineChart>
            ) : (
              <BarChart data={attendanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <Legend />
                <Bar dataKey="present" stackId="a" fill="#00C49F" name="Present" />
                <Bar dataKey="late" stackId="a" fill="#FFBB28" name="Late" />
                <Bar dataKey="excused" stackId="a" fill="#8884D8" name="Excused" />
                <Bar dataKey="absent" stackId="a" fill="#FF8042" name="Absent" />
              </BarChart>
            )}
          </ResponsiveContainer>
        )}
      </Card>

      {/* Attendance Distribution and Group Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h4 className="text-lg font-medium mb-4">Attendance Status Distribution</h4>
          {loading ? (
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          )}
        </Card>

        <Card className="p-6">
          <h4 className="text-lg font-medium mb-4">Group Attendance Performance</h4>
          {loading ? (
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Loading chart data...</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={groupData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" domain={[0, 100]} />
                <YAxis dataKey="group" type="category" width={100} />
                <Tooltip formatter={(value) => [`${value}%`, "Attendance Rate"]} />
                <Bar dataKey="attendanceRate" fill="#0088FE" />
              </BarChart>
            </ResponsiveContainer>
          )}
        </Card>
      </div>

      {/* Group Performance Table */}
      <Card className="p-6">
        <h4 className="text-lg font-medium mb-4">Detailed Group Performance</h4>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2">Group</th>
                <th className="text-left py-2">Course</th>
                <th className="text-right py-2">Attendance Rate</th>
                <th className="text-right py-2">Total Classes</th>
                <th className="text-right py-2">Avg. Attendance</th>
                <th className="text-right py-2">Status</th>
              </tr>
            </thead>
            <tbody>
              {groupData.map((group) => {
                const getStatusColor = (rate: number) => {
                  if (rate >= 90) return "text-green-600"
                  if (rate >= 80) return "text-yellow-600"
                  return "text-red-600"
                }
                
                const getStatusText = (rate: number) => {
                  if (rate >= 90) return "Excellent"
                  if (rate >= 80) return "Good"
                  return "Needs Attention"
                }

                return (
                  <tr key={group.group} className="border-b">
                    <td className="py-2 font-medium">{group.group}</td>
                    <td className="py-2">{group.course}</td>
                    <td className={`text-right py-2 font-medium ${getStatusColor(group.attendanceRate)}`}>
                      {group.attendanceRate}%
                    </td>
                    <td className="text-right py-2">{group.totalClasses}</td>
                    <td className="text-right py-2">{group.averageAttendance}</td>
                    <td className={`text-right py-2 font-medium ${getStatusColor(group.attendanceRate)}`}>
                      {getStatusText(group.attendanceRate)}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Status Legend */}
      <Card className="p-4">
        <div className="flex flex-wrap gap-6 justify-center">
          {statusData.map((status, index) => (
            <div key={status.name} className="flex items-center space-x-2">
              <div
                className="w-4 h-4 rounded"
                style={{ backgroundColor: status.color }}
              />
              <span className="text-sm font-medium">{status.name}</span>
              <span className="text-sm text-gray-500">({status.value})</span>
            </div>
          ))}
        </div>
      </Card>
    </div>
  )
}
