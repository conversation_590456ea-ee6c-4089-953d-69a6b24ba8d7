const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3003';

async function testEndpoint(endpoint, description) {
  try {
    console.log(`\n🧪 Testing: ${description}`);
    console.log(`📍 Endpoint: ${endpoint}`);
    
    const response = await fetch(`${BASE_URL}${endpoint}`);
    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ Success: ${response.status}`);
      
      // Show data counts for different endpoints
      if (data.leads) {
        console.log(`   📊 Found ${data.leads.length} leads`);
      } else if (data.students) {
        console.log(`   📊 Found ${data.students.length} students`);
      } else if (data.groups) {
        console.log(`   📊 Found ${data.groups.length} groups`);
      } else if (data.teachers) {
        console.log(`   📊 Found ${data.teachers.length} teachers`);
      } else if (data.payments) {
        console.log(`   📊 Found ${data.payments.length} payments`);
      } else {
        console.log(`   📊 Response received`);
      }
    } else {
      console.log(`❌ Error: ${response.status} - ${data.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`);
  }
}

async function runTests() {
  console.log('🚀 Testing API Endpoints with Branch Filtering\n');
  console.log('=' .repeat(60));
  
  // Test Main Branch endpoints
  console.log('\n🏢 MAIN BRANCH TESTS');
  await testEndpoint('/api/leads?branch=main', 'Leads - Main Branch');
  await testEndpoint('/api/students?branch=main', 'Students - Main Branch');
  await testEndpoint('/api/groups?branch=main', 'Groups - Main Branch');
  await testEndpoint('/api/teachers?branch=main', 'Teachers - Main Branch');
  
  // Test Branch endpoints
  console.log('\n🏢 BRANCH TESTS');
  await testEndpoint('/api/leads?branch=branch', 'Leads - Branch');
  await testEndpoint('/api/students?branch=branch', 'Students - Branch');
  await testEndpoint('/api/groups?branch=branch', 'Groups - Branch');
  await testEndpoint('/api/teachers?branch=branch', 'Teachers - Branch');
  
  // Test specific lead statuses
  console.log('\n📋 LEAD STATUS TESTS');
  await testEndpoint('/api/leads?branch=main&status=NEW', 'NEW Leads - Main Branch');
  await testEndpoint('/api/leads?branch=main&status=CALLING', 'CALLING Leads - Main Branch');
  await testEndpoint('/api/leads?branch=branch&status=NEW', 'NEW Leads - Branch');
  
  console.log('\n' + '=' .repeat(60));
  console.log('✅ API Testing Complete!');
  console.log('\n💡 If you see data counts above, the branch filtering is working correctly.');
  console.log('💡 You should now see leads and other data in the frontend.');
}

runTests().catch(console.error);
