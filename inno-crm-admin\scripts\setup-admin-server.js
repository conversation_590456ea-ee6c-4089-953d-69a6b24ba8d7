#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Admin Server configuration...\n');

// Function to copy file with modifications
function copyAndModifyFile(source, destination, modifications = {}) {
  try {
    let content = fs.readFileSync(source, 'utf8');
    
    // Apply modifications
    Object.keys(modifications).forEach(key => {
      content = content.replace(new RegExp(key, 'g'), modifications[key]);
    });
    
    fs.writeFileSync(destination, content);
    console.log(`✅ Created: ${destination}`);
  } catch (error) {
    console.log(`❌ Error creating ${destination}:`, error.message);
  }
}

// Function to update JSON file
function updateJsonFile(filePath, updates) {
  try {
    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    const updatedContent = { ...content, ...updates };
    fs.writeFileSync(filePath, JSON.stringify(updatedContent, null, 2));
    console.log(`✅ Updated: ${filePath}`);
  } catch (error) {
    console.log(`❌ Error updating ${filePath}:`, error.message);
  }
}

// 1. Update package.json for admin server
console.log('📦 Updating package.json for admin server...');
updateJsonFile('package.json', {
  name: 'inno-crm-admin',
  scripts: {
    dev: 'next dev',
    build: 'prisma generate && next build',
    start: 'next start',
    lint: 'next lint',
    'db:push': 'prisma db push',
    'db:studio': 'prisma studio',
    'db:generate': 'prisma generate',
    'db:seed': 'tsx prisma/seed.ts',
    postinstall: 'prisma generate',
    'prepare-deployment': 'node scripts/prepare-deployment.js'
  }
});

// 2. Copy admin vercel configuration
console.log('\n🔧 Setting up Vercel configuration...');
if (fs.existsSync('vercel.admin.json')) {
  fs.copyFileSync('vercel.admin.json', 'vercel.json');
  console.log('✅ Copied vercel.admin.json to vercel.json');
} else {
  console.log('❌ vercel.admin.json not found');
}

// 3. Copy admin environment configuration
console.log('\n🔐 Setting up environment configuration...');
if (fs.existsSync('.env.production.admin')) {
  fs.copyFileSync('.env.production.admin', '.env.production');
  console.log('✅ Copied .env.production.admin to .env.production');
} else {
  console.log('❌ .env.production.admin not found');
}

// 4. Update README.md for admin server
console.log('\n📖 Updating README.md...');
copyAndModifyFile('README.md', 'README.md', {
  'Innovative Centre CRM System - Staff Server': 'Innovative Centre CRM System - Admin Server',
  'staff server': 'admin server',
  'Staff Server': 'Admin Server',
  'reception and teaching staff with limited functionality': 'administrators and cashiers with full system access',
  'excludes admin and cashier roles': 'includes admin and cashier roles with full access',
  'day-to-day operations': 'comprehensive system administration',
  'Manager, Teacher, Reception, Student, Academic Manager \\(Staff Server\\)': 'Admin, Cashier, Manager, Teacher, Reception, Student, Academic Manager (Admin Server)',
  'port 3001': 'port 3000'
});

// 5. Create admin-specific middleware configuration
console.log('\n🛡️ Creating admin middleware configuration...');
const middlewareContent = `// Admin Server Middleware Configuration
// This file should be used to update middleware.ts for admin server

export const ADMIN_SERVER_CONFIG = {
  allowedRoles: ['ADMIN', 'CASHIER', 'MANAGER', 'TEACHER', 'RECEPTION', 'ACADEMIC_MANAGER', 'STUDENT'],
  adminOnlyRoutes: [
    '/dashboard/admin',
    '/dashboard/analytics',
    '/dashboard/payments',
    '/api/analytics',
    '/api/reports',
    '/api/payments',
    '/api/kpis',
    '/api/activity-logs'
  ],
  serverType: 'admin'
};

// Update your middleware.ts to use this configuration
// Example:
// const config = process.env.SERVER_TYPE === 'admin' ? ADMIN_SERVER_CONFIG : STAFF_SERVER_CONFIG;
`;

fs.writeFileSync('middleware.admin.config.js', middlewareContent);
console.log('✅ Created middleware.admin.config.js');

// 6. Create deployment checklist
console.log('\n📋 Creating deployment checklist...');
const checklistContent = `# Admin Server Deployment Checklist

## Pre-Deployment Steps
- [ ] Admin database created and configured
- [ ] Environment variables updated in .env.production
- [ ] NEXTAUTH_URL updated to admin domain
- [ ] DATABASE_URL points to admin database
- [ ] Inter-server communication secrets configured

## Repository Setup
- [ ] Repository pushed to GitHub as inno-crm-admin
- [ ] All admin configuration files in place
- [ ] package.json updated with admin settings
- [ ] vercel.json configured for admin server

## Vercel Deployment
- [ ] Project imported to Vercel as inno-crm-admin
- [ ] Environment variables set in Vercel dashboard
- [ ] Build and deployment successful
- [ ] Health check endpoint responding

## Testing
- [ ] Admin login working
- [ ] Cashier login working
- [ ] All admin features accessible
- [ ] Analytics dashboard working
- [ ] Payment management working
- [ ] Financial reports working
- [ ] Inter-server communication working

## Security
- [ ] IP whitelist configured (if needed)
- [ ] CORS settings verified
- [ ] Authentication working properly
- [ ] Role-based access control verified

## Monitoring
- [ ] Health checks setup
- [ ] Error monitoring configured
- [ ] Performance monitoring active
- [ ] Database connection monitoring

## Documentation
- [ ] Admin server documentation updated
- [ ] Deployment guide reviewed
- [ ] Team notified of admin server URL
- [ ] Access credentials distributed securely
`;

fs.writeFileSync('ADMIN_DEPLOYMENT_CHECKLIST.md', checklistContent);
console.log('✅ Created ADMIN_DEPLOYMENT_CHECKLIST.md');

// 7. Summary
console.log('\n🎉 Admin Server setup complete!\n');
console.log('📝 Next Steps:');
console.log('1. Create admin database (Neon or other provider)');
console.log('2. Update DATABASE_URL in .env.production');
console.log('3. Update NEXTAUTH_URL to your admin domain');
console.log('4. Push to GitHub as inno-crm-admin repository');
console.log('5. Deploy to Vercel');
console.log('6. Test admin functionality');
console.log('\n📖 See ADMIN_SERVER_SETUP.md for detailed instructions');
console.log('📋 Use ADMIN_DEPLOYMENT_CHECKLIST.md to track progress');
