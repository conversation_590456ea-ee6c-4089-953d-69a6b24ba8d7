import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const teacherSchema = z.object({
  userId: z.string(),
  subject: z.string().min(1),
  experience: z.number().min(0).optional(),
  branch: z.string().min(1),
  photoUrl: z.string().optional(),
  tier: z.enum(['A_LEVEL', 'B_LEVEL', 'C_LEVEL', 'NEW']).default('NEW'),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const subject = searchParams.get('subject')
    const branch = searchParams.get('branch')

    const where: any = {}

    if (search) {
      where.OR = [
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { phone: { contains: search } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
        { subject: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (subject) {
      where.subject = { contains: subject, mode: 'insensitive' }
    }

    if (branch) {
      // Map branch ID to branch name for database query
      const branchName = branch === 'main' ? 'Main Branch' : 'Branch'
      where.branch = branchName
    }

    const [teachers, total] = await Promise.all([
      prisma.teacher.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
              role: true,
              createdAt: true,
            },
          },
          groups: {
            include: {
              course: {
                select: {
                  name: true,
                  level: true,
                },
              },
              _count: {
                select: {
                  enrollments: true,
                },
              },
            },
          },
          classes: {
            include: {
              group: {
                select: {
                  name: true,
                },
              },
            },
            orderBy: { date: 'desc' },
            take: 5,
          },
          _count: {
            select: {
              groups: true,
              classes: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.teacher.count({ where }),
    ])

    return NextResponse.json({
      teachers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching teachers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = teacherSchema.parse(body)

    // Check if user exists and has TEACHER role
    const user = await prisma.user.findUnique({
      where: { id: validatedData.userId },
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 400 }
      )
    }

    if (user.role !== 'TEACHER') {
      return NextResponse.json(
        { error: 'User must have TEACHER role' },
        { status: 400 }
      )
    }

    // Check if teacher profile already exists for this user
    const existingTeacher = await prisma.teacher.findUnique({
      where: { userId: validatedData.userId },
    })

    if (existingTeacher) {
      return NextResponse.json(
        { error: 'Teacher profile already exists for this user' },
        { status: 400 }
      )
    }

    const teacher = await prisma.teacher.create({
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            role: true,
          },
        },
        _count: {
          select: {
            groups: true,
            classes: true,
          },
        },
      },
    })

    return NextResponse.json(teacher, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating teacher:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
