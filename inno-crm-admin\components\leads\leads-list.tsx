'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Phone, User, Calendar, Clock, Users, MapPin, Archive, Play, Pause, Download, Volume2 } from 'lucide-react'
import { formatDateTime, formatDate } from '@/lib/utils'
import CallManager from './call-manager'
import GroupAssignmentModal from './group-assignment-modal'

interface Lead {
  id: string
  name: string
  phone: string
  coursePreference: string
  status: string
  source?: string
  notes?: string
  createdAt: string
  updatedAt: string
  callStartedAt?: string
  callEndedAt?: string
  callDuration?: number
  assignedAt?: string
  archivedAt?: string
  assignedGroup?: {
    name: string
    course: {
      name: string
      level: string
    }
    teacher: {
      user: {
        name: string
      }
    }
  }
  assignedTeacher?: {
    user: {
      name: string
    }
  }
  callRecords?: Array<{
    id: string
    startedAt: string
    endedAt?: string
    duration?: number
    notes?: string
    recordingUrl?: string
  }>
}

interface LeadsListProps {
  leads: Lead[]
  onLeadUpdate: () => void
  onError: (error: string) => void
  isArchiveView?: boolean
}

export default function LeadsList({ leads, onLeadUpdate, onError, isArchiveView = false }: LeadsListProps) {
  const [selectedCallLead, setSelectedCallLead] = useState<Lead | null>(null)
  const [selectedAssignLead, setSelectedAssignLead] = useState<Lead | null>(null)
  const [isMounted, setIsMounted] = useState(false)
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null)
  const [currentPlayingUrl, setCurrentPlayingUrl] = useState<string | null>(null)

  useEffect(() => {
    setIsMounted(true)
    return () => {
      // Cleanup audio when component unmounts
      if (currentAudio) {
        currentAudio.pause()
        currentAudio.src = ''
      }
    }
  }, [currentAudio])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-blue-100 text-blue-800'
      case 'CALLING':
        return 'bg-yellow-100 text-yellow-800'
      case 'CALL_COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'GROUP_ASSIGNED':
        return 'bg-purple-100 text-purple-800'
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800'
      case 'NOT_INTERESTED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    return status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const canCall = (lead: Lead) => {
    return lead.status === 'NEW' && !isArchiveView
  }

  const canAssignGroup = (lead: Lead) => {
    return lead.status === 'CALL_COMPLETED' && !isArchiveView
  }

  const handleArchiveLead = async (leadId: string) => {
    try {
      const response = await fetch(`/api/leads/${leadId}/archive`, {
        method: 'POST',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to archive lead')
      }

      onLeadUpdate()
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to archive lead')
    }
  }

  const formatCallDuration = (seconds?: number) => {
    if (!seconds) return 'N/A'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const handlePlayRecording = (recordingUrl: string) => {
    // If this recording is already playing, pause it
    if (currentPlayingUrl === recordingUrl && currentAudio) {
      currentAudio.pause()
      setCurrentAudio(null)
      setCurrentPlayingUrl(null)
      return
    }

    // Stop current audio if playing
    if (currentAudio) {
      currentAudio.pause()
      currentAudio.currentTime = 0
    }

    // Create new audio instance
    const audio = new Audio(recordingUrl)
    setCurrentAudio(audio)
    setCurrentPlayingUrl(recordingUrl)

    audio.play().catch(error => {
      console.error('Error playing audio:', error)
      onError('Failed to play recording: ' + error.message)
      setCurrentAudio(null)
      setCurrentPlayingUrl(null)
    })

    // Clean up when audio ends
    audio.addEventListener('ended', () => {
      setCurrentAudio(null)
      setCurrentPlayingUrl(null)
    })

    // Clean up on error
    audio.addEventListener('error', () => {
      setCurrentAudio(null)
      setCurrentPlayingUrl(null)
      onError('Error loading audio file')
    })
  }

  const handleDownloadRecording = (recordingUrl: string, leadName: string, recordId: string) => {
    // Create download link
    const link = document.createElement('a')
    link.href = recordingUrl
    link.download = `call-recording-${leadName}-${recordId}.mp3`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Prevent hydration errors by only rendering on client
  if (!isMounted) {
    return (
      <div className="space-y-4">
        <div className="border rounded-lg overflow-hidden">
          <div className="p-8 text-center text-gray-500">
            Loading leads...
          </div>
        </div>
      </div>
    )
  }

  if (leads.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500">
          {isArchiveView ? 'No archived leads found' : 'No leads found'}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Lead Info</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Call Info</TableHead>
              <TableHead>Recordings</TableHead>
              <TableHead>Assigned Group</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {leads.map((lead) => (
              <TableRow key={lead.id} className="hover:bg-gray-50">
                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium flex items-center gap-2">
                      <User className="h-4 w-4" />
                      {lead.name}
                    </div>
                    <div className="text-sm text-gray-600 flex items-center gap-1">
                      <Phone className="h-3 w-3" />
                      {lead.phone}
                    </div>
                    <div className="text-xs text-gray-500 flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(lead.createdAt)}
                    </div>
                    {lead.source && (
                      <div className="text-xs text-gray-500">
                        Source: {lead.source}
                      </div>
                    )}
                  </div>
                </TableCell>

                <TableCell>
                  <Badge className={getStatusColor(lead.status)}>
                    {getStatusText(lead.status)}
                  </Badge>
                </TableCell>

                <TableCell>
                  {lead.callDuration ? (
                    <div className="space-y-1">
                      <div className="text-sm flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatCallDuration(lead.callDuration)}
                      </div>
                      {lead.callStartedAt && (
                        <div className="text-xs text-gray-500">
                          {formatDateTime(lead.callStartedAt)}
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400 text-sm">No calls</span>
                  )}
                </TableCell>

                <TableCell>
                  {lead.callRecords && lead.callRecords.length > 0 ? (
                    <div className="space-y-2">
                      {lead.callRecords.map((record) => (
                        <div key={record.id} className="flex items-center gap-2">
                          {record.recordingUrl ? (
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handlePlayRecording(record.recordingUrl!)}
                                className={`h-6 w-6 p-0 ${currentPlayingUrl === record.recordingUrl ? 'bg-green-100 border-green-300' : ''}`}
                              >
                                {currentPlayingUrl === record.recordingUrl ? (
                                  <Pause className="h-3 w-3" />
                                ) : (
                                  <Play className="h-3 w-3" />
                                )}
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDownloadRecording(record.recordingUrl!, lead.name, record.id)}
                                className="h-6 w-6 p-0"
                              >
                                <Download className="h-3 w-3" />
                              </Button>
                            </div>
                          ) : (
                            <span className="text-xs text-gray-400">No recording</span>
                          )}
                          <div className="text-xs text-gray-500">
                            {formatCallDuration(record.duration)}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <span className="text-gray-400 text-sm">No recordings</span>
                  )}
                </TableCell>

                <TableCell>
                  {lead.assignedGroup ? (
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{lead.assignedGroup.name}</div>
                      <div className="text-xs text-gray-600">
                        {lead.assignedGroup.course.name} - {lead.assignedGroup.course.level}
                      </div>
                      <div className="text-xs text-gray-600 flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {lead.assignedGroup.teacher.user.name}
                      </div>
                      {lead.assignedAt && (
                        <div className="text-xs text-gray-500">
                          {formatDateTime(lead.assignedAt)}
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400 text-sm">Not assigned</span>
                  )}
                </TableCell>

                <TableCell>
                  <div className="flex gap-1">
                    {canCall(lead) && (
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button size="sm" className="bg-green-600 hover:bg-green-700">
                            <Phone className="h-3 w-3" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <CallManager
                            leadId={lead.id}
                            leadName={lead.name}
                            leadPhone={lead.phone}
                            onCallComplete={() => {
                              onLeadUpdate()
                              setSelectedCallLead(null)
                            }}
                            onError={onError}
                          />
                        </DialogContent>
                      </Dialog>
                    )}

                    {canAssignGroup(lead) && (
                      <Button
                        size="sm"
                        onClick={() => setSelectedAssignLead(lead)}
                        className="bg-purple-600 hover:bg-purple-700"
                      >
                        <Users className="h-3 w-3" />
                      </Button>
                    )}

                    {lead.status === 'GROUP_ASSIGNED' && !isArchiveView && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleArchiveLead(lead.id)}
                      >
                        <Archive className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Group Assignment Modal */}
      {selectedAssignLead && (
        <GroupAssignmentModal
          isOpen={!!selectedAssignLead}
          onClose={() => setSelectedAssignLead(null)}
          leadId={selectedAssignLead.id}
          leadName={selectedAssignLead.name}
          onAssignmentComplete={onLeadUpdate}
          onError={onError}
        />
      )}
    </div>
  )
}
