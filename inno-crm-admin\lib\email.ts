// Email service using Nodemail<PERSON> with support for multiple providers
import nodemailer from 'nodemailer'

interface EmailConfig {
  provider: 'gmail' | 'outlook' | 'smtp'
  host?: string
  port?: number
  secure?: boolean
  user: string
  password: string
  from?: string
}

interface EmailMessage {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  attachments?: Array<{
    filename: string
    content: Buffer | string
    contentType?: string
  }>
}

interface EmailResponse {
  success: boolean
  messageId?: string
  error?: string
}

class EmailService {
  private transporter: nodemailer.Transporter
  private config: EmailConfig

  constructor(config: EmailConfig) {
    this.config = config
    this.transporter = this.createTransporter()
  }

  private createTransporter(): nodemailer.Transporter {
    const transportConfig: any = {
      auth: {
        user: this.config.user,
        pass: this.config.password,
      },
    }

    switch (this.config.provider) {
      case 'gmail':
        transportConfig.service = 'gmail'
        break
      case 'outlook':
        transportConfig.service = 'hotmail'
        break
      case 'smtp':
        transportConfig.host = this.config.host
        transportConfig.port = this.config.port || 587
        transportConfig.secure = this.config.secure || false
        break
    }

    return nodemailer.createTransport(transportConfig)
  }

  async sendEmail(message: EmailMessage): Promise<EmailResponse> {
    try {
      const mailOptions = {
        from: this.config.from || this.config.user,
        to: Array.isArray(message.to) ? message.to.join(', ') : message.to,
        subject: message.subject,
        html: message.html,
        text: message.text,
        attachments: message.attachments,
      }

      const result = await this.transporter.sendMail(mailOptions)

      return {
        success: true,
        messageId: result.messageId,
      }
    } catch (error) {
      console.error('Email sending failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify()
      return true
    } catch (error) {
      console.error('Email connection verification failed:', error)
      return false
    }
  }
}

// Email templates for common use cases
export const EMAIL_TEMPLATES = {
  ENROLLMENT_CONFIRMATION: (studentName: string, courseName: string, startDate: string, groupName: string) => ({
    subject: `Welcome to ${courseName} - Enrollment Confirmation`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">Innovative Centre</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">English Language Learning</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Enrollment Confirmation</h2>
          
          <p>Dear ${studentName},</p>
          
          <p>Congratulations! You have been successfully enrolled in our <strong>${courseName}</strong> course.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">Course Details</h3>
            <p><strong>Course:</strong> ${courseName}</p>
            <p><strong>Group:</strong> ${groupName}</p>
            <p><strong>Start Date:</strong> ${startDate}</p>
          </div>
          
          <p>Please make sure to:</p>
          <ul>
            <li>Arrive 10 minutes before class starts</li>
            <li>Bring necessary materials (notebook, pen)</li>
            <li>Complete your payment if not already done</li>
          </ul>
          
          <p>If you have any questions, please don't hesitate to contact us.</p>
          
          <p>Best regards,<br>Innovative Centre Team</p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Innovative Centre | English Language Learning</p>
          <p>Phone: +998 90 123 45 67 | Email: <EMAIL></p>
        </div>
      </div>
    `,
  }),

  PAYMENT_CONFIRMATION: (studentName: string, amount: number, courseName: string, paymentMethod: string, transactionId?: string) => ({
    subject: `Payment Confirmation - ${amount.toLocaleString()} UZS`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">Payment Confirmed</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">Innovative Centre</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Payment Receipt</h2>
          
          <p>Dear ${studentName},</p>
          
          <p>Thank you for your payment. We have successfully received your payment for the ${courseName} course.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h3 style="margin-top: 0; color: #28a745;">Payment Details</h3>
            <p><strong>Amount:</strong> ${amount.toLocaleString()} UZS</p>
            <p><strong>Course:</strong> ${courseName}</p>
            <p><strong>Payment Method:</strong> ${paymentMethod}</p>
            ${transactionId ? `<p><strong>Transaction ID:</strong> ${transactionId}</p>` : ''}
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
          </div>
          
          <p>This email serves as your payment receipt. Please keep it for your records.</p>
          
          <p>Best regards,<br>Innovative Centre Team</p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Innovative Centre | English Language Learning</p>
          <p>Phone: +998 90 123 45 67 | Email: <EMAIL></p>
        </div>
      </div>
    `,
  }),

  COURSE_COMPLETION: (studentName: string, courseName: string, completionDate: string, nextLevel?: string) => ({
    subject: `Congratulations! Course Completion Certificate - ${courseName}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%); color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">🎉 Congratulations!</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">Course Completion</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Course Completion Certificate</h2>
          
          <p>Dear ${studentName},</p>
          
          <p>Congratulations on successfully completing the <strong>${courseName}</strong> course!</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <h3 style="margin-top: 0; color: #ffc107;">Achievement Details</h3>
            <p><strong>Course:</strong> ${courseName}</p>
            <p><strong>Completion Date:</strong> ${completionDate}</p>
            <p><strong>Student:</strong> ${studentName}</p>
          </div>
          
          ${nextLevel ? `
            <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #1976d2;">Next Step</h3>
              <p>Ready for the next challenge? Consider enrolling in <strong>${nextLevel}</strong> to continue your English learning journey!</p>
            </div>
          ` : ''}
          
          <p>Your certificate will be available for download from your student portal, or you can collect a printed copy from our office.</p>
          
          <p>Thank you for choosing Innovative Centre for your English language learning journey!</p>
          
          <p>Best regards,<br>Innovative Centre Team</p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Innovative Centre | English Language Learning</p>
          <p>Phone: +998 90 123 45 67 | Email: <EMAIL></p>
        </div>
      </div>
    `,
  }),

  PAYMENT_REMINDER: (studentName: string, amount: number, dueDate: string, courseName: string) => ({
    subject: `Payment Reminder - ${amount.toLocaleString()} UZS Due`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">Payment Reminder</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">Innovative Centre</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Payment Due Notice</h2>
          
          <p>Dear ${studentName},</p>
          
          <p>This is a friendly reminder that your payment for the ${courseName} course is due.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;">
            <h3 style="margin-top: 0; color: #dc3545;">Payment Details</h3>
            <p><strong>Amount Due:</strong> ${amount.toLocaleString()} UZS</p>
            <p><strong>Course:</strong> ${courseName}</p>
            <p><strong>Due Date:</strong> ${dueDate}</p>
          </div>
          
          <p>Please make your payment as soon as possible to avoid any interruption to your classes.</p>
          
          <p>Payment methods available:</p>
          <ul>
            <li>Cash at our office</li>
            <li>UzCard/Humo</li>
            <li>PayMe/Click</li>
            <li>Bank transfer</li>
          </ul>
          
          <p>If you have already made the payment, please ignore this reminder.</p>
          
          <p>Best regards,<br>Innovative Centre Team</p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Innovative Centre | English Language Learning</p>
          <p>Phone: +998 90 123 45 67 | Email: <EMAIL></p>
        </div>
      </div>
    `,
  }),
}

// Initialize email service with environment variables
export function createEmailService(): EmailService {
  const provider = (process.env.EMAIL_PROVIDER as any) || 'gmail'
  
  const config: EmailConfig = {
    provider,
    user: process.env.EMAIL_USER || '',
    password: process.env.EMAIL_PASSWORD || '',
    from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
  }

  if (provider === 'smtp') {
    config.host = process.env.SMTP_HOST
    config.port = parseInt(process.env.SMTP_PORT || '587')
    config.secure = process.env.SMTP_SECURE === 'true'
  }

  return new EmailService(config)
}

export { EmailService }
export type { EmailMessage, EmailResponse, EmailConfig }
