const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createSampleData() {
  try {
    console.log('Creating sample data for testing branch separation...')

    // Hash password for sample users
    const hashedPassword = await bcrypt.hash('password123', 12)

    // Create sample courses
    console.log('Creating sample courses...')
    const ieltsCourse = await prisma.course.upsert({
      where: { name: 'IELTS Preparation' },
      update: {},
      create: {
        name: 'IELTS Preparation',
        level: 'IELTS',
        description: 'Comprehensive IELTS preparation course',
        duration: 120,
        price: 1500000
      }
    })

    const mathCourse = await prisma.course.upsert({
      where: { name: 'Mathematics' },
      update: {},
      create: {
        name: 'Mathematics',
        level: 'MATH',
        description: 'Advanced mathematics course',
        duration: 100,
        price: 1200000
      }
    })

    // Create sample teachers for Main Branch
    console.log('Creating sample teachers for Main Branch...')
    const mainTeacherUser = await prisma.user.upsert({
      where: { phone: '+998901234567' },
      update: {},
      create: {
        phone: '+998901234567',
        name: '<PERSON>',
        email: '<EMAIL>',
        role: 'TEACHER',
        password: hashedPassword
      }
    })

    const mainTeacher = await prisma.teacher.upsert({
      where: { userId: mainTeacherUser.id },
      update: {},
      create: {
        userId: mainTeacherUser.id,
        subject: 'IELTS',
        experience: 5,
        salary: 5000000,
        branch: 'main'
      }
    })

    // Create sample teachers for Branch
    console.log('Creating sample teachers for Branch...')
    const branchTeacherUser = await prisma.user.upsert({
      where: { phone: '+998907654321' },
      update: {},
      create: {
        phone: '+998907654321',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'TEACHER',
        password: hashedPassword
      }
    })

    const branchTeacher = await prisma.teacher.upsert({
      where: { userId: branchTeacherUser.id },
      update: {},
      create: {
        userId: branchTeacherUser.id,
        subject: 'Mathematics',
        experience: 3,
        salary: 4500000,
        branch: 'branch'
      }
    })

    // Create sample students for Main Branch
    console.log('Creating sample students for Main Branch...')
    const mainStudentUser = await prisma.user.upsert({
      where: { phone: '+998911111111' },
      update: {},
      create: {
        phone: '+998911111111',
        name: 'Alice Cooper',
        email: '<EMAIL>',
        role: 'STUDENT',
        password: hashedPassword
      }
    })

    const mainStudent = await prisma.student.upsert({
      where: { userId: mainStudentUser.id },
      update: {},
      create: {
        userId: mainStudentUser.id,
        level: 'IELTS',
        branch: 'main',
        status: 'ACTIVE'
      }
    })

    // Create sample students for Branch
    console.log('Creating sample students for Branch...')
    const branchStudentUser = await prisma.user.upsert({
      where: { phone: '+998922222222' },
      update: {},
      create: {
        phone: '+998922222222',
        name: 'Bob Wilson',
        email: '<EMAIL>',
        role: 'STUDENT',
        password: hashedPassword
      }
    })

    const branchStudent = await prisma.student.upsert({
      where: { userId: branchStudentUser.id },
      update: {},
      create: {
        userId: branchStudentUser.id,
        level: 'MATH',
        branch: 'branch',
        status: 'ACTIVE'
      }
    })

    // Create sample groups for Main Branch
    console.log('Creating sample groups for Main Branch...')
    const mainGroup = await prisma.group.upsert({
      where: { name: 'IELTS Group A - Main' },
      update: {},
      create: {
        name: 'IELTS Group A - Main',
        courseId: ieltsCourse.id,
        teacherId: mainTeacher.id,
        capacity: 15,
        schedule: 'Mon/Wed/Fri 10:00-12:00',
        branch: 'main',
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-06-15'),
        isActive: true
      }
    })

    // Create sample groups for Branch
    console.log('Creating sample groups for Branch...')
    const branchGroup = await prisma.group.upsert({
      where: { name: 'Math Group B - Branch' },
      update: {},
      create: {
        name: 'Math Group B - Branch',
        courseId: mathCourse.id,
        teacherId: branchTeacher.id,
        capacity: 12,
        schedule: 'Tue/Thu/Sat 14:00-16:00',
        branch: 'branch',
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-07-01'),
        isActive: true
      }
    })

    // Create sample enrollments
    console.log('Creating sample enrollments...')
    await prisma.enrollment.upsert({
      where: { 
        studentId_groupId: {
          studentId: mainStudent.id,
          groupId: mainGroup.id
        }
      },
      update: {},
      create: {
        studentId: mainStudent.id,
        groupId: mainGroup.id,
        status: 'ACTIVE',
        startDate: new Date('2024-01-15')
      }
    })

    await prisma.enrollment.upsert({
      where: { 
        studentId_groupId: {
          studentId: branchStudent.id,
          groupId: branchGroup.id
        }
      },
      update: {},
      create: {
        studentId: branchStudent.id,
        groupId: branchGroup.id,
        status: 'ACTIVE',
        startDate: new Date('2024-02-01')
      }
    })

    // Create sample payments
    console.log('Creating sample payments...')
    await prisma.payment.create({
      data: {
        studentId: mainStudent.id,
        amount: 1500000,
        method: 'CASH',
        status: 'PAID',
        description: 'IELTS course payment - Main Branch',
        paidDate: new Date()
      }
    })

    await prisma.payment.create({
      data: {
        studentId: branchStudent.id,
        amount: 1200000,
        method: 'UZCARD',
        status: 'PAID',
        description: 'Math course payment - Branch',
        paidDate: new Date()
      }
    })

    // Create sample assessments
    console.log('Creating sample assessments...')
    await prisma.assessment.create({
      data: {
        studentId: mainStudent.id,
        testName: 'IELTS Level Test',
        type: 'LEVEL_TEST',
        level: 'IELTS',
        score: 85,
        maxScore: 100,
        passed: true,
        branch: 'main',
        completedAt: new Date()
      }
    })

    await prisma.assessment.create({
      data: {
        studentId: branchStudent.id,
        testName: 'Math Level Test',
        type: 'LEVEL_TEST',
        level: 'MATH',
        score: 78,
        maxScore: 100,
        passed: true,
        branch: 'branch',
        completedAt: new Date()
      }
    })

    console.log('✅ Sample data created successfully!')
    console.log('\n📊 Summary:')
    console.log('- Main Branch: 1 teacher, 1 student, 1 group, 1 payment, 1 assessment')
    console.log('- Branch: 1 teacher, 1 student, 1 group, 1 payment, 1 assessment')
    console.log('- 2 courses created')
    console.log('\n🔐 Test Credentials:')
    console.log('Admin: +998906006299 / Parviz0106$')
    console.log('Main Teacher: +998901234567 / password123')
    console.log('Branch Teacher: +998907654321 / password123')
    console.log('Main Student: +998911111111 / password123')
    console.log('Branch Student: +998922222222 / password123')

  } catch (error) {
    console.error('❌ Error creating sample data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSampleData()
