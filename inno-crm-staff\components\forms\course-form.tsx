'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Switch } from '@/components/ui/switch'
import { Loader2, BookOpen, GraduationCap, Clock, DollarSign } from 'lucide-react'

const courseSchema = z.object({
  name: z.string().min(2, 'Course name must be at least 2 characters'),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']),
  description: z.string().optional(),
  duration: z.number().min(1, 'Duration must be at least 1 week').max(52, 'Duration cannot exceed 52 weeks'),
  price: z.number().min(0, 'Price cannot be negative'),
  isActive: z.boolean().default(true),
})

type CourseFormData = z.infer<typeof courseSchema>

interface CourseFormProps {
  initialData?: Partial<CourseFormData>
  onSubmit: (data: CourseFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
}

const levels = [
  { value: 'A1', label: 'A1 - Beginner', description: 'Basic level for complete beginners' },
  { value: 'A2', label: 'A2 - Elementary', description: 'Elementary level with basic communication skills' },
  { value: 'B1', label: 'B1 - Intermediate', description: 'Intermediate level for everyday communication' },
  { value: 'B2', label: 'B2 - Upper Intermediate', description: 'Upper intermediate with complex topics' },
  { value: 'IELTS', label: 'IELTS', description: 'IELTS preparation course' },
  { value: 'SAT', label: 'SAT Preparation', description: 'SAT test preparation course' },
  { value: 'MATH', label: 'Mathematics', description: 'Mathematics course for various levels' },
  { value: 'KIDS', label: 'Kids English', description: 'English course designed for children' },
]

const durationPresets = [
  { weeks: 4, label: '1 Month (4 weeks)' },
  { weeks: 8, label: '2 Months (8 weeks)' },
  { weeks: 12, label: '3 Months (12 weeks)' },
  { weeks: 16, label: '4 Months (16 weeks)' },
  { weeks: 20, label: '5 Months (20 weeks)' },
  { weeks: 24, label: '6 Months (24 weeks)' },
  { weeks: 36, label: '9 Months (36 weeks)' },
  { weeks: 48, label: '1 Year (48 weeks)' },
]

const pricePresets = [
  { price: 500000, label: '$40/month (500,000 UZS)' },
  { price: 625000, label: '$50/month (625,000 UZS)' },
  { price: 750000, label: '$60/month (750,000 UZS)' },
  { price: 875000, label: '$70/month (875,000 UZS)' },
  { price: 1000000, label: '$80/month (1,000,000 UZS)' },
  { price: 1250000, label: '$100/month (1,250,000 UZS)' },
  { price: 1500000, label: '$120/month (1,500,000 UZS)' },
  { price: 2000000, label: '$160/month (2,000,000 UZS)' },
]

export default function CourseForm({ initialData, onSubmit, onCancel, isEditing = false }: CourseFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CourseFormData>({
    resolver: zodResolver(courseSchema),
    defaultValues: {
      name: initialData?.name || '',
      level: initialData?.level || 'A1',
      description: initialData?.description || '',
      duration: initialData?.duration || 12,
      price: initialData?.price || 750000,
      isActive: initialData?.isActive ?? true,
    },
  })

  const selectedLevel = watch('level')
  const duration = watch('duration')
  const price = watch('price')
  const isActive = watch('isActive')

  const handleFormSubmit = async (data: CourseFormData) => {
    setIsSubmitting(true)
    setError(null)

    try {
      await onSubmit(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedLevelInfo = levels.find(l => l.value === selectedLevel)

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <BookOpen className="h-5 w-5 mr-2" />
          {isEditing ? 'Edit Course' : 'Create New Course'}
        </CardTitle>
        <CardDescription>
          {isEditing ? 'Update course information' : 'Enter course details to create a new course offering'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Basic Information Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <BookOpen className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Course Information</h3>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Course Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="e.g., General English A1, IELTS Preparation"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="level">Level *</Label>
                <Select
                  value={selectedLevel}
                  onValueChange={(value) => setValue('level', value as any)}
                >
                  <SelectTrigger className={errors.level ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select level" />
                  </SelectTrigger>
                  <SelectContent>
                    {levels.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.level && (
                  <p className="text-sm text-red-500">{errors.level.message}</p>
                )}
                {selectedLevelInfo && (
                  <p className="text-sm text-gray-600">{selectedLevelInfo.description}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Describe the course content, objectives, and target audience..."
                  className="min-h-[100px]"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={isActive}
                  onCheckedChange={(checked) => setValue('isActive', checked)}
                />
                <Label htmlFor="isActive">Active Course</Label>
              </div>
            </div>
          </div>

          {/* Duration Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Clock className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Duration & Pricing</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="duration">Duration (weeks) *</Label>
                <Input
                  id="duration"
                  type="number"
                  min="1"
                  max="52"
                  {...register('duration', { valueAsNumber: true })}
                  className={errors.duration ? 'border-red-500' : ''}
                />
                {errors.duration && (
                  <p className="text-sm text-red-500">{errors.duration.message}</p>
                )}
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {durationPresets.slice(0, 4).map((preset) => (
                    <Button
                      key={preset.weeks}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setValue('duration', preset.weeks)}
                      className={duration === preset.weeks ? 'bg-blue-50 border-blue-300' : ''}
                    >
                      {preset.weeks}w
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="price">Price (UZS) *</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="price"
                    type="number"
                    min="0"
                    step="50000"
                    {...register('price', { valueAsNumber: true })}
                    className={`pl-10 ${errors.price ? 'border-red-500' : ''}`}
                  />
                </div>
                {errors.price && (
                  <p className="text-sm text-red-500">{errors.price.message}</p>
                )}
                <p className="text-sm text-gray-600">
                  ≈ ${(price / 12500).toFixed(0)} USD
                </p>
              </div>
            </div>

            {/* Price Presets */}
            <div className="space-y-2">
              <Label>Quick Price Selection</Label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {pricePresets.map((preset) => (
                  <Button
                    key={preset.price}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setValue('price', preset.price)}
                    className={price === preset.price ? 'bg-green-50 border-green-300' : ''}
                  >
                    ${(preset.price / 12500).toFixed(0)}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Course Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Course Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Level:</span>
                <span className="ml-2 font-medium">{selectedLevelInfo?.label}</span>
              </div>
              <div>
                <span className="text-gray-600">Duration:</span>
                <span className="ml-2 font-medium">{duration} weeks</span>
              </div>
              <div>
                <span className="text-gray-600">Price:</span>
                <span className="ml-2 font-medium">{price.toLocaleString()} UZS (${(price / 12500).toFixed(0)})</span>
              </div>
              <div>
                <span className="text-gray-600">Status:</span>
                <span className={`ml-2 font-medium ${isActive ? 'text-green-600' : 'text-red-600'}`}>
                  {isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update Course' : 'Create Course'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
