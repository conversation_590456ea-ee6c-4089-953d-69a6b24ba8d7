/**
 * Verification Script for Comprehensive Leads Management System
 * 
 * This script verifies that all components and API endpoints are properly implemented
 * without requiring a database connection.
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 Verifying Comprehensive Leads Management Implementation...\n')

// Files to check
const requiredFiles = [
  // Database
  'prisma/schema.prisma',
  
  // API Endpoints
  'app/api/leads/route.ts',
  'app/api/leads/[id]/route.ts',
  'app/api/leads/[id]/call/route.ts',
  'app/api/leads/[id]/assign-group/route.ts',
  'app/api/leads/[id]/archive/route.ts',
  'app/api/leads/cleanup/route.ts',
  
  // UI Components
  'components/leads/date-filter.tsx',
  'components/leads/call-manager.tsx',
  'components/leads/group-assignment-modal.tsx',
  'components/leads/leads-list.tsx',
  
  // Pages
  'app/(dashboard)/dashboard/leads/page.tsx',
  
  // Documentation
  'docs/comprehensive-leads-management.md',
  'MIGRATION_GUIDE.md',
  'IMPLEMENTATION_STATUS.md',
  'COMPREHENSIVE_LEADS_MANAGEMENT_IMPLEMENTATION.md'
]

// Check if files exist
let allFilesExist = true
console.log('📁 Checking required files...')

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`)
  } else {
    console.log(`❌ ${file} - MISSING`)
    allFilesExist = false
  }
})

console.log('')

// Check schema for new models and fields
console.log('🗄️  Checking database schema...')

try {
  const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8')
  
  const checks = [
    { name: 'CallRecord model', pattern: /model CallRecord/ },
    { name: 'Lead.callStartedAt field', pattern: /callStartedAt\s+DateTime\?/ },
    { name: 'Lead.assignedGroupId field', pattern: /assignedGroupId\s+String\?/ },
    { name: 'Lead.archivedAt field', pattern: /archivedAt\s+DateTime\?/ },
    { name: 'Updated LeadStatus enum', pattern: /enum LeadStatus[\s\S]*CALLING[\s\S]*CALL_COMPLETED[\s\S]*GROUP_ASSIGNED[\s\S]*ARCHIVED/ },
    { name: 'CallRecord relations', pattern: /callRecords\s+CallRecord\[\]/ }
  ]
  
  checks.forEach(check => {
    if (check.pattern.test(schemaContent)) {
      console.log(`✅ ${check.name}`)
    } else {
      console.log(`❌ ${check.name} - NOT FOUND`)
      allFilesExist = false
    }
  })
} catch (error) {
  console.log(`❌ Error reading schema: ${error.message}`)
  allFilesExist = false
}

console.log('')

// Check API endpoints for required exports
console.log('🔌 Checking API endpoints...')

const apiChecks = [
  {
    file: 'app/api/leads/[id]/call/route.ts',
    exports: ['POST', 'PUT'],
    patterns: [/export async function POST/, /export async function PUT/]
  },
  {
    file: 'app/api/leads/[id]/assign-group/route.ts',
    exports: ['GET', 'POST'],
    patterns: [/export async function GET/, /export async function POST/]
  },
  {
    file: 'app/api/leads/[id]/archive/route.ts',
    exports: ['POST', 'DELETE'],
    patterns: [/export async function POST/, /export async function DELETE/]
  },
  {
    file: 'app/api/leads/cleanup/route.ts',
    exports: ['GET', 'POST'],
    patterns: [/export async function GET/, /export async function POST/]
  }
]

apiChecks.forEach(check => {
  try {
    if (fs.existsSync(check.file)) {
      const content = fs.readFileSync(check.file, 'utf8')
      
      check.patterns.forEach((pattern, index) => {
        if (pattern.test(content)) {
          console.log(`✅ ${check.file} - ${check.exports[index]} method`)
        } else {
          console.log(`❌ ${check.file} - ${check.exports[index]} method - NOT FOUND`)
          allFilesExist = false
        }
      })
    }
  } catch (error) {
    console.log(`❌ Error checking ${check.file}: ${error.message}`)
    allFilesExist = false
  }
})

console.log('')

// Check React components for required exports
console.log('⚛️  Checking React components...')

const componentChecks = [
  {
    file: 'components/leads/date-filter.tsx',
    name: 'DateFilter',
    pattern: /export default function DateFilter/
  },
  {
    file: 'components/leads/call-manager.tsx',
    name: 'CallManager',
    pattern: /export default function CallManager/
  },
  {
    file: 'components/leads/group-assignment-modal.tsx',
    name: 'GroupAssignmentModal',
    pattern: /export default function GroupAssignmentModal/
  },
  {
    file: 'components/leads/leads-list.tsx',
    name: 'LeadsList',
    pattern: /export default function LeadsList/
  }
]

componentChecks.forEach(check => {
  try {
    if (fs.existsSync(check.file)) {
      const content = fs.readFileSync(check.file, 'utf8')
      
      if (check.pattern.test(content)) {
        console.log(`✅ ${check.name} component`)
      } else {
        console.log(`❌ ${check.name} component - EXPORT NOT FOUND`)
        allFilesExist = false
      }
    }
  } catch (error) {
    console.log(`❌ Error checking ${check.file}: ${error.message}`)
    allFilesExist = false
  }
})

console.log('')

// Check main leads page for new imports
console.log('📄 Checking main leads page...')

try {
  const leadsPageContent = fs.readFileSync('app/(dashboard)/dashboard/leads/page.tsx', 'utf8')
  
  const pageChecks = [
    { name: 'DateFilter import', pattern: /import.*DateFilter.*from.*date-filter/ },
    { name: 'LeadsList import', pattern: /import.*LeadsList.*from.*leads-list/ },
    { name: 'Tabs component usage', pattern: /<Tabs/ },
    { name: 'Archive tab', pattern: /TabsTrigger.*archived/ },
    { name: 'Date filter usage', pattern: /<DateFilter/ }
  ]
  
  pageChecks.forEach(check => {
    if (check.pattern.test(leadsPageContent)) {
      console.log(`✅ ${check.name}`)
    } else {
      console.log(`❌ ${check.name} - NOT FOUND`)
      allFilesExist = false
    }
  })
} catch (error) {
  console.log(`❌ Error checking leads page: ${error.message}`)
  allFilesExist = false
}

console.log('')

// Check package.json for required dependencies
console.log('📦 Checking dependencies...')

try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
  
  const requiredDeps = [
    '@prisma/client',
    'prisma',
    'zod',
    'next-auth',
    'lucide-react'
  ]
  
  requiredDeps.forEach(dep => {
    if (dependencies[dep]) {
      console.log(`✅ ${dep}`)
    } else {
      console.log(`❌ ${dep} - NOT FOUND`)
      allFilesExist = false
    }
  })
} catch (error) {
  console.log(`❌ Error checking package.json: ${error.message}`)
  allFilesExist = false
}

console.log('')

// Final verification
console.log('🎯 Verification Summary:')
console.log('========================')

if (allFilesExist) {
  console.log('✅ ALL CHECKS PASSED')
  console.log('🎉 Implementation is complete and ready for deployment!')
  console.log('')
  console.log('Next steps:')
  console.log('1. Set up PostgreSQL database')
  console.log('2. Run: npx prisma migrate dev --name comprehensive-leads-management')
  console.log('3. Run: npm run dev')
  console.log('4. Test the new leads management system')
  console.log('5. Follow the MIGRATION_GUIDE.md for production deployment')
} else {
  console.log('❌ SOME CHECKS FAILED')
  console.log('Please review the missing files or components above.')
  process.exit(1)
}

console.log('')
console.log('📚 Documentation available:')
console.log('- IMPLEMENTATION_STATUS.md - Complete implementation status')
console.log('- MIGRATION_GUIDE.md - Database migration instructions')
console.log('- docs/comprehensive-leads-management.md - System documentation')
console.log('- scripts/test-leads-management.js - Testing script')
