// Automated business workflows for Innovative Centre
import { prisma } from './prisma'
import { getNotificationService } from './notifications'

interface WorkflowTrigger {
  event: string
  conditions?: Record<string, any>
  delay?: number // in minutes
}

interface WorkflowAction {
  type: 'notification' | 'update_status' | 'create_record' | 'send_reminder'
  data: Record<string, any>
}

interface Workflow {
  id: string
  name: string
  description: string
  trigger: WorkflowTrigger
  actions: WorkflowAction[]
  enabled: boolean
}

class WorkflowEngine {
  private workflows: Workflow[] = []
  private notificationService = getNotificationService()

  constructor() {
    this.initializeDefaultWorkflows()
  }

  private initializeDefaultWorkflows() {
    this.workflows = [
      // Enrollment Confirmation Workflow
      {
        id: 'enrollment-confirmation',
        name: 'Enrollment Confirmation',
        description: 'Send confirmation when student enrolls in a course',
        trigger: {
          event: 'enrollment.created',
        },
        actions: [
          {
            type: 'notification',
            data: {
              type: 'enrollment',
              priority: 'high',
            },
          },
        ],
        enabled: true,
      },

      // Payment Confirmation Workflow
      {
        id: 'payment-confirmation',
        name: 'Payment Confirmation',
        description: 'Send confirmation when payment is received',
        trigger: {
          event: 'payment.created',
          conditions: { status: 'PAID' },
        },
        actions: [
          {
            type: 'notification',
            data: {
              type: 'payment',
              priority: 'medium',
            },
          },
        ],
        enabled: true,
      },

      // Payment Reminder Workflow
      {
        id: 'payment-reminder',
        name: 'Payment Reminder',
        description: 'Send reminder for overdue payments',
        trigger: {
          event: 'payment.overdue',
        },
        actions: [
          {
            type: 'notification',
            data: {
              type: 'payment_reminder',
              priority: 'high',
            },
          },
          {
            type: 'update_status',
            data: {
              status: 'DEBT',
            },
          },
        ],
        enabled: true,
      },

      // Class Reminder Workflow
      {
        id: 'class-reminder',
        name: 'Class Reminder',
        description: 'Send reminder 2 hours before class',
        trigger: {
          event: 'class.upcoming',
          delay: 120, // 2 hours before
        },
        actions: [
          {
            type: 'notification',
            data: {
              type: 'class_reminder',
              priority: 'medium',
              channels: ['sms'],
            },
          },
        ],
        enabled: true,
      },

      // Attendance Alert Workflow
      {
        id: 'attendance-alert',
        name: 'Attendance Alert',
        description: 'Alert parents when student is absent',
        trigger: {
          event: 'attendance.marked',
          conditions: { status: 'ABSENT' },
        },
        actions: [
          {
            type: 'notification',
            data: {
              type: 'attendance_alert',
              priority: 'high',
              channels: ['sms'],
            },
          },
        ],
        enabled: true,
      },

      // Course Completion Workflow
      {
        id: 'course-completion',
        name: 'Course Completion',
        description: 'Congratulate student on course completion',
        trigger: {
          event: 'enrollment.completed',
        },
        actions: [
          {
            type: 'notification',
            data: {
              type: 'completion',
              priority: 'medium',
            },
          },
          {
            type: 'create_record',
            data: {
              type: 'certificate',
            },
          },
        ],
        enabled: true,
      },

      // Lead Follow-up Workflow
      {
        id: 'lead-followup',
        name: 'Lead Follow-up',
        description: 'Follow up with leads after 24 hours',
        trigger: {
          event: 'lead.created',
          delay: 1440, // 24 hours
        },
        actions: [
          {
            type: 'send_reminder',
            data: {
              type: 'lead_followup',
              assignee: 'reception',
            },
          },
        ],
        enabled: true,
      },

      // Monthly Payment Reminder
      {
        id: 'monthly-payment-reminder',
        name: 'Monthly Payment Reminder',
        description: 'Remind students about upcoming monthly payments',
        trigger: {
          event: 'payment.due_soon',
          delay: 10080, // 7 days before due date
        },
        actions: [
          {
            type: 'notification',
            data: {
              type: 'payment_reminder',
              priority: 'medium',
            },
          },
        ],
        enabled: true,
      },
    ]
  }

  async triggerWorkflow(event: string, data: any): Promise<void> {
    const applicableWorkflows = this.workflows.filter(
      workflow => 
        workflow.enabled && 
        workflow.trigger.event === event &&
        this.checkConditions(workflow.trigger.conditions, data)
    )

    for (const workflow of applicableWorkflows) {
      if (workflow.trigger.delay) {
        // Schedule for later execution
        setTimeout(() => {
          this.executeWorkflow(workflow, data)
        }, workflow.trigger.delay * 60 * 1000)
      } else {
        // Execute immediately
        await this.executeWorkflow(workflow, data)
      }
    }
  }

  private checkConditions(conditions: Record<string, any> | undefined, data: any): boolean {
    if (!conditions) return true

    return Object.entries(conditions).every(([key, value]) => {
      return data[key] === value
    })
  }

  private async executeWorkflow(workflow: Workflow, triggerData: any): Promise<void> {
    console.log(`Executing workflow: ${workflow.name}`)

    for (const action of workflow.actions) {
      try {
        await this.executeAction(action, triggerData)
      } catch (error) {
        console.error(`Failed to execute action in workflow ${workflow.name}:`, error)
      }
    }
  }

  private async executeAction(action: WorkflowAction, triggerData: any): Promise<void> {
    switch (action.type) {
      case 'notification':
        await this.executeNotificationAction(action, triggerData)
        break
      case 'update_status':
        await this.executeUpdateStatusAction(action, triggerData)
        break
      case 'create_record':
        await this.executeCreateRecordAction(action, triggerData)
        break
      case 'send_reminder':
        await this.executeSendReminderAction(action, triggerData)
        break
      default:
        console.warn(`Unknown action type: ${action.type}`)
    }
  }

  private async executeNotificationAction(action: WorkflowAction, triggerData: any): Promise<void> {
    const { type, priority, channels } = action.data

    // Get recipient information based on trigger data
    let recipient: any = null

    if (triggerData.studentId) {
      const student = await prisma.student.findUnique({
        where: { id: triggerData.studentId },
        include: { user: true },
      })
      if (student) {
        recipient = {
          id: student.id,
          name: student.user.name,
          phone: student.user.phone,
          email: student.user.email,
        }
      }
    }

    if (recipient) {
      await this.notificationService.sendNotification(recipient, {
        type,
        priority,
        channels,
        data: triggerData,
      })
    }
  }

  private async executeUpdateStatusAction(action: WorkflowAction, triggerData: any): Promise<void> {
    const { status } = action.data

    if (triggerData.paymentId) {
      await prisma.payment.update({
        where: { id: triggerData.paymentId },
        data: { status },
      })
    } else if (triggerData.enrollmentId) {
      await prisma.enrollment.update({
        where: { id: triggerData.enrollmentId },
        data: { status },
      })
    }
  }

  private async executeCreateRecordAction(action: WorkflowAction, triggerData: any): Promise<void> {
    const { type } = action.data

    if (type === 'certificate' && triggerData.enrollmentId) {
      // In a real implementation, you might create a certificate record
      console.log(`Creating certificate for enrollment: ${triggerData.enrollmentId}`)
    }
  }

  private async executeSendReminderAction(action: WorkflowAction, triggerData: any): Promise<void> {
    const { type, assignee } = action.data

    // In a real implementation, you might create a task or reminder for staff
    console.log(`Creating reminder: ${type} for ${assignee}`)
  }

  // Scheduled job methods
  async checkOverduePayments(): Promise<void> {
    const overduePayments = await prisma.payment.findMany({
      where: {
        status: 'DEBT',
        createdAt: {
          lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days old
        },
      },
      include: {
        student: {
          include: { user: true },
        },
      },
    })

    for (const payment of overduePayments) {
      await this.triggerWorkflow('payment.overdue', {
        paymentId: payment.id,
        studentId: payment.studentId,
        amount: payment.amount,
        dueDate: payment.dueDate,
        studentName: payment.student.user.name,
      })
    }
  }

  async checkUpcomingClasses(): Promise<void> {
    const upcomingClasses = await prisma.class.findMany({
      where: {
        date: {
          gte: new Date(),
          lte: new Date(Date.now() + 3 * 60 * 60 * 1000), // Next 3 hours
        },
      },
      include: {
        group: {
          include: {
            course: true,
            enrollments: {
              include: {
                student: {
                  include: { user: true },
                },
              },
            },
          },
        },
      },
    })

    for (const classItem of upcomingClasses) {
      const students = classItem.group.enrollments.map(enrollment => ({
        name: enrollment.student.user.name,
        phone: enrollment.student.user.phone,
        email: enrollment.student.user.email || undefined,
      }))

      await this.notificationService.sendClassReminder(students, {
        courseName: classItem.group.course.name,
        time: classItem.date.toLocaleTimeString(),
      })
    }
  }

  async checkPaymentsDueSoon(): Promise<void> {
    const paymentsDueSoon = await prisma.payment.findMany({
      where: {
        status: 'DEBT',
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
          lte: new Date(),
        },
      },
      include: {
        student: {
          include: { user: true },
        },
      },
    })

    for (const payment of paymentsDueSoon) {
      await this.triggerWorkflow('payment.due_soon', {
        paymentId: payment.id,
        studentId: payment.studentId,
        amount: payment.amount,
        dueDate: payment.dueDate,
        studentName: payment.student.user.name,
      })
    }
  }

  // Workflow management methods
  enableWorkflow(workflowId: string): void {
    const workflow = this.workflows.find(w => w.id === workflowId)
    if (workflow) {
      workflow.enabled = true
    }
  }

  disableWorkflow(workflowId: string): void {
    const workflow = this.workflows.find(w => w.id === workflowId)
    if (workflow) {
      workflow.enabled = false
    }
  }

  getWorkflows(): Workflow[] {
    return this.workflows
  }

  addWorkflow(workflow: Workflow): void {
    this.workflows.push(workflow)
  }
}

// Singleton instance
let workflowEngine: WorkflowEngine | null = null

export function getWorkflowEngine(): WorkflowEngine {
  if (!workflowEngine) {
    workflowEngine = new WorkflowEngine()
  }
  return workflowEngine
}

export { WorkflowEngine }
export type { Workflow, WorkflowTrigger, WorkflowAction }
