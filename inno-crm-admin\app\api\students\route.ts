import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const studentSchema = z.object({
  userId: z.string(),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']),
  branch: z.string(),
  emergencyContact: z.string().optional(),
  dateOfBirth: z.string().optional(),
  address: z.string().optional(),
  status: z.enum(['ACTIVE', 'DROPPED', 'PAUSED', 'COMPLETED']).default('ACTIVE'),
  currentGroupId: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const level = searchParams.get('level')
    const branch = searchParams.get('branch')
    const status = searchParams.get('status')
    const paymentStatus = searchParams.get('paymentStatus')
    const includeDropped = searchParams.get('includeDropped') === 'true'

    // Get students and total count
    const where: any = {}

    // Handle dropped students filter
    if (includeDropped) {
      where.status = 'DROPPED'
    } else if (!status) {
      // If not showing dropped students and no specific status filter, exclude dropped
      where.status = { not: 'DROPPED' }
    }

    if (search) {
      where.OR = [
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { phone: { contains: search } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
      ]
    }

    if (level) {
      where.level = level
    }

    if (branch) {
      // Map branch ID to branch name for database query
      const branchName = branch === 'main' ? 'Main Branch' : 'Branch'
      where.branch = branchName
    }

    if (status) {
      where.status = status
    }

    // Handle payment status filtering
    if (paymentStatus === 'UNPAID') {
      where.payments = {
        some: {
          status: 'DEBT'
        }
      }
    }

    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              phone: true,
              email: true,
              createdAt: true,
            },
          },
          enrollments: {
            include: {
              group: {
                include: {
                  course: {
                    select: {
                      name: true,
                      level: true,
                    },
                  },
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 5,
          },
          payments: {
            select: {
              status: true,
              amount: true,
              dueDate: true,
            },
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.student.count({ where }),
    ])

    // Get status counts
    const statusCounts = await prisma.student.groupBy({
      by: ['status'],
      _count: {
        status: true,
      },
    })

    // Calculate payment status for each student
    const studentsWithPaymentStatus = students.map(student => {
      const unpaidPayments = student.payments.filter(p =>
        p.status === 'DEBT'
      )
      const hasUnpaidPayments = unpaidPayments.length > 0

      return {
        ...student,
        paymentStatus: hasUnpaidPayments ? 'UNPAID' : 'PAID',
        unpaidAmount: unpaidPayments.reduce((sum, p) => sum + Number(p.amount), 0),
      }
    })

    return NextResponse.json({
      students: studentsWithPaymentStatus,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      statusCounts: statusCounts.reduce((acc, item) => {
        acc[item.status] = item._count.status
        return acc
      }, {} as Record<string, number>),
    })
  } catch (error) {
    console.error('Error fetching students:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = studentSchema.parse(body)

    const student = await prisma.student.create({
      data: {
        ...validatedData,
        dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : null,
      },
      include: {
        user: {
          select: {
            name: true,
            phone: true,
            email: true,
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.logStudentCreated(
      session.user.id,
      session.user.role as Role,
      student.id,
      {
        studentName: student.user.name,
        level: student.level,
        branch: student.branch,
      },
      request
    )

    return NextResponse.json(student, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating student:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
