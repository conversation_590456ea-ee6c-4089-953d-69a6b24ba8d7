const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyDataCounts() {
  try {
    console.log('🔍 Verifying Data Counts by Branch\n');
    console.log('=' .repeat(50));
    
    // Check leads by branch
    const leadsByBranch = await prisma.lead.groupBy({
      by: ['branch'],
      _count: { branch: true }
    });
    
    console.log('\n📋 LEADS BY BRANCH:');
    leadsByBranch.forEach(item => {
      console.log(`   ${item.branch}: ${item._count.branch} leads`);
    });
    
    // Check students by branch
    const studentsByBranch = await prisma.student.groupBy({
      by: ['branch'],
      _count: { branch: true }
    });
    
    console.log('\n👥 STUDENTS BY BRANCH:');
    studentsByBranch.forEach(item => {
      console.log(`   ${item.branch}: ${item._count.branch} students`);
    });
    
    // Check groups by branch
    const groupsByBranch = await prisma.group.groupBy({
      by: ['branch'],
      _count: { branch: true }
    });
    
    console.log('\n📚 GROUPS BY BRANCH:');
    groupsByBranch.forEach(item => {
      console.log(`   ${item.branch}: ${item._count.branch} groups`);
    });
    
    // Check teachers by branch
    const teachersByBranch = await prisma.teacher.groupBy({
      by: ['branch'],
      _count: { branch: true }
    });
    
    console.log('\n👨‍🏫 TEACHERS BY BRANCH:');
    teachersByBranch.forEach(item => {
      console.log(`   ${item.branch}: ${item._count.branch} teachers`);
    });
    
    // Check lead statuses for Main Branch
    const mainBranchLeadStatuses = await prisma.lead.groupBy({
      by: ['status'],
      where: { branch: 'Main Branch' },
      _count: { status: true }
    });
    
    console.log('\n📊 MAIN BRANCH LEAD STATUSES:');
    mainBranchLeadStatuses.forEach(item => {
      console.log(`   ${item.status}: ${item._count.status} leads`);
    });
    
    // Check lead statuses for Branch
    const branchLeadStatuses = await prisma.lead.groupBy({
      by: ['status'],
      where: { branch: 'Branch' },
      _count: { status: true }
    });
    
    console.log('\n📊 BRANCH LEAD STATUSES:');
    branchLeadStatuses.forEach(item => {
      console.log(`   ${item.status}: ${item._count.status} leads`);
    });
    
    console.log('\n' + '=' .repeat(50));
    console.log('✅ Data verification complete!');
    console.log('\n💡 The frontend should now show this data when you:');
    console.log('   1. Switch between Main Branch and Branch');
    console.log('   2. Filter by different lead statuses');
    console.log('   3. Navigate between Students, Groups, etc.');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyDataCounts();
