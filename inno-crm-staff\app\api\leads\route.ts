import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const leadSchema = z.object({
  name: z.string().min(2),
  phone: z.string().min(9),
  coursePreference: z.string().min(1),
  branch: z.string().optional().default('main'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = leadSchema.parse(body)

    const lead = await prisma.lead.create({
      data: {
        name: validatedData.name,
        phone: validatedData.phone,
        coursePreference: validatedData.coursePreference,
        branch: validatedData.branch,
        source: 'Website',
        status: 'NEW',
      },
    })

    return NextResponse.json(lead, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const dateFilter = searchParams.get('dateFilter')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const archived = searchParams.get('archived') === 'true'
    const branchId = searchParams.get('branch') || 'main'

    // Map branch ID to branch name for database query
    const branchName = branchId === 'main' ? 'Main Branch' : 'Branch'

    const where: any = {
      branch: branchName
    }

    // Status filter
    if (status && status !== 'ALL') {
      where.status = status
    }

    // Archive filter
    if (archived) {
      where.archivedAt = { not: null }
    } else {
      where.archivedAt = null
    }

    // Date filter
    if (dateFilter || (startDate && endDate)) {
      const now = new Date()
      let filterStartDate: Date
      let filterEndDate: Date = now

      if (dateFilter) {
        switch (dateFilter) {
          case 'today':
            filterStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
            break
          case 'yesterday':
            const yesterday = new Date(now)
            yesterday.setDate(yesterday.getDate() - 1)
            filterStartDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
            filterEndDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59)
            break
          case 'last7days':
            filterStartDate = new Date(now)
            filterStartDate.setDate(filterStartDate.getDate() - 7)
            break
          case 'last30days':
            filterStartDate = new Date(now)
            filterStartDate.setDate(filterStartDate.getDate() - 30)
            break
          default:
            filterStartDate = new Date(0) // Beginning of time
        }
      } else {
        filterStartDate = new Date(startDate!)
        filterEndDate = new Date(endDate!)
      }

      where.createdAt = {
        gte: filterStartDate,
        lte: filterEndDate,
      }
    }

    const [leads, total] = await Promise.all([
      prisma.lead.findMany({
        where,
        include: {
          assignedGroup: {
            include: {
              course: { select: { name: true, level: true } },
              teacher: { include: { user: { select: { name: true } } } }
            }
          },
          assignedTeacher: {
            include: { user: { select: { name: true } } }
          },
          callRecords: {
            orderBy: { createdAt: 'desc' },
            select: {
              id: true,
              startedAt: true,
              endedAt: true,
              duration: true,
              notes: true,
              recordingUrl: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.lead.count({ where }),
    ])

    return NextResponse.json({
      leads,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching leads:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
