const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function verifyAdminName() {
  try {
    console.log('🔍 Checking current admin user...')
    
    const adminUser = await prisma.user.findUnique({
      where: { phone: '+998906006299' }
    })
    
    if (adminUser) {
      console.log('✅ Admin user found:')
      console.log(`   Name: ${adminUser.name}`)
      console.log(`   Phone: ${adminUser.phone}`)
      console.log(`   Role: ${adminUser.role}`)
      console.log(`   Email: ${adminUser.email}`)
      
      if (adminUser.name === 'Parviz Adashov') {
        console.log('\n🎉 SUCCESS: Admin name is correctly set to "Par<PERSON>z Adashov"')
      } else {
        console.log('\n⚠️  WARNING: Admin name is not "Parviz Adashov"')
      }
    } else {
      console.log('❌ Admin user not found with phone +998906006299')
    }
    
  } catch (error) {
    console.error('❌ Error checking admin user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

verifyAdminName()
