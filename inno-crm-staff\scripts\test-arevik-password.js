require('dotenv').config({ path: '.env.local' });
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testArevikPassword() {
  try {
    console.log('Testing Arevik password...');

    // Find the user
    const user = await prisma.user.findUnique({
      where: { phone: '+998912345678' }
    });

    if (!user) {
      console.log('User not found');
      return;
    }

    console.log('User found:', {
      id: user.id,
      phone: user.phone,
      name: user.name,
      role: user.role,
    });

    // Test password
    const password = 'Arevik0106$';
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    console.log('Password test result:', isPasswordValid);
    console.log('Stored password hash:', user.password);

    // Test with different variations
    const variations = ['arevik0106$', 'Arevik0106', 'arevik0106'];
    for (const variation of variations) {
      const isValid = await bcrypt.compare(variation, user.password);
      console.log(`Password "${variation}": ${isValid}`);
    }

  } catch (error) {
    console.error('Error testing password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testArevikPassword();
