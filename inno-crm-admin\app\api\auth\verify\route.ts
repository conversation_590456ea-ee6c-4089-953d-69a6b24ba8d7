import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { z } from 'zod'

const verifyUserSchema = z.object({
  phone: z.string().min(9, 'Phone number must be at least 9 characters'),
  password: z.string().min(1, 'Password is required'),
  serverKey: z.string().min(1, 'Server key is required'),
})

// This endpoint allows the staff server to verify user credentials
export async function POST(request: NextRequest) {
  try {
    console.log('Verification endpoint called')
    const body = await request.json()
    console.log('Request body:', { phone: body.phone, serverKey: body.serverKey ? 'present' : 'missing' })
    const { phone, password, serverKey } = verifyUserSchema.parse(body)

    // Verify the server key to ensure only authorized servers can access this endpoint
    const expectedServerKey = process.env.INTER_SERVER_SECRET
    console.log('Server key check:', { expected: expectedServerKey ? 'present' : 'missing', received: serverKey })
    if (!expectedServerKey || serverKey !== expectedServerKey) {
      console.log('Server key mismatch')
      return NextResponse.json(
        { error: 'Unauthorized server access' },
        { status: 401 }
      )
    }

    // Find the user by phone
    const user = await prisma.user.findUnique({
      where: { phone },
      select: {
        id: true,
        phone: true,
        name: true,
        email: true,
        role: true,
        password: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Verify the password
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Check if the user role is allowed on staff server
    const allowedRoles = ['MANAGER', 'TEACHER', 'RECEPTION', 'STUDENT', 'ACADEMIC_MANAGER']
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Access denied: Role not allowed on staff server' },
        { status: 403 }
      )
    }

    // Return user data without password
    const { password: _, ...userWithoutPassword } = user
    return NextResponse.json({
      success: true,
      user: userWithoutPassword,
    })

  } catch (error) {
    console.error('Auth verification error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
