import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current date ranges
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
    const startOfWeek = new Date(now)
    startOfWeek.setDate(now.getDate() - 7)

    // Get total students count (without status filter for now)
    const totalStudents = await prisma.student.count()

    // Get last month's student count for comparison
    const lastMonthStudents = await prisma.student.count({
      where: {
        createdAt: { lte: endOfLastMonth }
      }
    })

    // Calculate student growth percentage
    const studentGrowth = lastMonthStudents > 0 
      ? Math.round(((totalStudents - lastMonthStudents) / lastMonthStudents) * 100)
      : 0

    // Get new leads this week (with error handling)
    let newLeads = 0
    let lastWeekLeads = 0
    try {
      newLeads = await prisma.lead.count({
        where: {
          createdAt: { gte: startOfWeek }
        }
      })

      // Get last week's leads for comparison
      const lastWeekStart = new Date(startOfWeek)
      lastWeekStart.setDate(lastWeekStart.getDate() - 7)
      lastWeekLeads = await prisma.lead.count({
        where: {
          createdAt: {
            gte: lastWeekStart,
            lt: startOfWeek
          }
        }
      })
    } catch (error) {
      console.log('Leads table not available:', error)
    }

    // Calculate leads growth percentage
    const leadsGrowth = lastWeekLeads > 0
      ? Math.round(((newLeads - lastWeekLeads) / lastWeekLeads) * 100)
      : 0

    // Get active groups count (with error handling)
    let activeGroups = 0
    try {
      activeGroups = await prisma.group.count({
        where: { isActive: true }
      })
    } catch (error) {
      console.log('Groups table not available:', error)
    }

    // Get monthly revenue (with error handling)
    let currentRevenue = 0
    let previousRevenue = 0
    try {
      const monthlyRevenue = await prisma.payment.aggregate({
        where: {
          status: 'PAID',
          createdAt: {
            gte: startOfMonth,
            lte: now
          }
        },
        _sum: { amount: true }
      })

      // Get last month's revenue for comparison
      const lastMonthRevenue = await prisma.payment.aggregate({
        where: {
          status: 'PAID',
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        },
        _sum: { amount: true }
      })

      currentRevenue = Number(monthlyRevenue._sum.amount) || 0
      previousRevenue = Number(lastMonthRevenue._sum.amount) || 0
    } catch (error) {
      console.log('Payments table not available:', error)
    }

    // Calculate revenue growth percentage
    const revenueGrowth = previousRevenue > 0
      ? Math.round(((currentRevenue - previousRevenue) / previousRevenue) * 100)
      : 0

    // Get recent leads (last 4) with error handling
    let recentLeads: any[] = []
    try {
      recentLeads = await prisma.lead.findMany({
        orderBy: { createdAt: 'desc' },
        take: 4,
        select: {
          id: true,
          name: true,
          coursePreference: true,
          createdAt: true
        }
      })
    } catch (error) {
      console.log('Recent leads not available:', error)
    }

    // Get upcoming classes for today (with error handling)
    let upcomingClasses: any[] = []
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      upcomingClasses = await prisma.class.findMany({
        where: {
          date: {
            gte: today,
            lt: tomorrow
          }
        },
        include: {
          group: {
            include: {
              course: { select: { name: true, level: true } },
              teacher: { include: { user: { select: { name: true } } } }
            }
          }
        },
        orderBy: { date: 'asc' },
        take: 4
      })
    } catch (error) {
      console.log('Classes table not available:', error)
    }

    // Format the response
    const stats = {
      totalStudents: {
        count: totalStudents,
        growth: studentGrowth
      },
      newLeads: {
        count: newLeads,
        growth: leadsGrowth
      },
      activeGroups: {
        count: activeGroups
      },
      monthlyRevenue: {
        amount: currentRevenue,
        growth: revenueGrowth
      },
      recentLeads: recentLeads.map(lead => ({
        name: lead.name,
        course: lead.coursePreference,
        status: 'NEW',
        time: getTimeAgo(lead.createdAt)
      })),
      upcomingClasses: upcomingClasses.map(classItem => ({
        group: `${classItem.group?.course?.name || 'Unknown Course'} - ${classItem.group?.course?.level || 'Unknown Level'}`,
        teacher: classItem.group?.teacher?.user?.name || 'No Teacher',
        time: classItem.date ? formatTime(classItem.date.toISOString()) : 'TBA',
        room: 'TBA'
      }))
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}

function formatTime(dateString: string): string {
  const date = new Date(dateString)
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
}
