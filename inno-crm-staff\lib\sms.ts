// SMS Integration for Uzbekistan providers
// Supports Eskiz.uz, SMS.uz, and other local providers

interface SMSConfig {
  provider: 'eskiz' | 'sms_uz' | 'playmobile'
  apiKey: string
  apiUrl: string
  from?: string
}

interface SMSMessage {
  to: string
  message: string
  from?: string
}

interface SMSResponse {
  success: boolean
  messageId?: string
  error?: string
  cost?: number
}

class SMSService {
  private config: SMSConfig

  constructor(config: SMSConfig) {
    this.config = config
  }

  async sendSMS(message: SMSMessage): Promise<SMSResponse> {
    try {
      switch (this.config.provider) {
        case 'eskiz':
          return await this.sendEskizSMS(message)
        case 'sms_uz':
          return await this.sendSMSUzSMS(message)
        case 'playmobile':
          return await this.sendPlaymobileSMS(message)
        default:
          throw new Error(`Unsupported SMS provider: ${this.config.provider}`)
      }
    } catch (error) {
      console.error('SMS sending failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  private async sendEskizSMS(message: SMSMessage): Promise<SMSResponse> {
    const response = await fetch(`${this.config.apiUrl}/api/message/sms/send`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        mobile_phone: this.formatPhoneNumber(message.to),
        message: message.message,
        from: message.from || this.config.from || '4546',
      }),
    })

    const data = await response.json()

    if (response.ok && data.status === 'success') {
      return {
        success: true,
        messageId: data.data.id,
        cost: data.data.cost,
      }
    }

    return {
      success: false,
      error: data.message || 'Failed to send SMS via Eskiz'
    }
  }

  private async sendSMSUzSMS(message: SMSMessage): Promise<SMSResponse> {
    const response = await fetch(`${this.config.apiUrl}/api/v1/send`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: this.formatPhoneNumber(message.to),
        text: message.message,
        sender: message.from || this.config.from || 'SMS.UZ',
      }),
    })

    const data = await response.json()

    if (response.ok && data.success) {
      return {
        success: true,
        messageId: data.data.message_id,
      }
    }

    return {
      success: false,
      error: data.error || 'Failed to send SMS via SMS.uz'
    }
  }

  private async sendPlaymobileSMS(message: SMSMessage): Promise<SMSResponse> {
    const response = await fetch(`${this.config.apiUrl}/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        login: this.config.apiKey.split(':')[0],
        password: this.config.apiKey.split(':')[1],
        data: JSON.stringify([{
          phone: this.formatPhoneNumber(message.to),
          text: message.message,
        }]),
      }),
    })

    const data = await response.json()

    if (response.ok && data.result === 'OK') {
      return {
        success: true,
        messageId: data.data[0]?.id,
      }
    }

    return {
      success: false,
      error: data.error || 'Failed to send SMS via Playmobile'
    }
  }

  private formatPhoneNumber(phone: string): string {
    // Remove all non-digits
    const digits = phone.replace(/\D/g, '')
    
    // Ensure it starts with 998 for Uzbekistan
    if (digits.startsWith('998')) {
      return digits
    } else if (digits.length === 9) {
      return '998' + digits
    } else if (digits.length === 12 && digits.startsWith('998')) {
      return digits
    }
    
    throw new Error(`Invalid phone number format: ${phone}`)
  }

  async getBalance(): Promise<{ balance: number; currency: string } | null> {
    try {
      switch (this.config.provider) {
        case 'eskiz':
          return await this.getEskizBalance()
        case 'sms_uz':
          return await this.getSMSUzBalance()
        default:
          return null
      }
    } catch (error) {
      console.error('Failed to get SMS balance:', error)
      return null
    }
  }

  private async getEskizBalance(): Promise<{ balance: number; currency: string }> {
    const response = await fetch(`${this.config.apiUrl}/api/user/get-limit`, {
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
      },
    })

    const data = await response.json()
    
    if (response.ok && data.status === 'success') {
      return {
        balance: data.data.balance,
        currency: 'UZS'
      }
    }

    throw new Error('Failed to get balance')
  }

  private async getSMSUzBalance(): Promise<{ balance: number; currency: string }> {
    const response = await fetch(`${this.config.apiUrl}/api/v1/balance`, {
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
      },
    })

    const data = await response.json()
    
    if (response.ok && data.success) {
      return {
        balance: data.data.balance,
        currency: 'UZS'
      }
    }

    throw new Error('Failed to get balance')
  }
}

// SMS Templates for common use cases
export const SMS_TEMPLATES = {
  ENROLLMENT_CONFIRMATION: (studentName: string, courseName: string, startDate: string) =>
    `Assalomu alaykum ${studentName}! Siz ${courseName} kursiga muvaffaqiyatli ro'yxatdan o'tdingiz. Darslar ${startDate} dan boshlanadi. Innovative Centre`,

  PAYMENT_CONFIRMATION: (studentName: string, amount: number, courseName: string) =>
    `${studentName}, ${amount.toLocaleString()} so'm to'lov qabul qilindi. Kurs: ${courseName}. Rahmat! Innovative Centre`,

  CLASS_REMINDER: (studentName: string, courseName: string, time: string) =>
    `${studentName}, bugun ${time} da ${courseName} darsi bor. Kechikmaslik uchun iltimos! Innovative Centre`,

  PAYMENT_REMINDER: (studentName: string, amount: number, dueDate: string) =>
    `${studentName}, ${amount.toLocaleString()} so'm to'lov muddati ${dueDate} gacha. Iltimos, o'z vaqtida to'lang. Innovative Centre`,

  ATTENDANCE_ALERT: (parentName: string, studentName: string, courseName: string) =>
    `Hurmatli ${parentName}, ${studentName} bugun ${courseName} darsida qatnashmadi. Innovative Centre`,

  COURSE_COMPLETION: (studentName: string, courseName: string, nextLevel: string) =>
    `Tabriklaymiz ${studentName}! ${courseName} kursini muvaffaqiyatli yakunladingiz. Keyingi bosqich: ${nextLevel}. Innovative Centre`,
}

// Initialize SMS service with environment variables
export function createSMSService(): SMSService {
  const provider = (process.env.SMS_PROVIDER as 'eskiz' | 'sms_uz' | 'playmobile') || 'eskiz'
  const apiKey = process.env.SMS_API_KEY || ''

  const configs = {
    eskiz: {
      provider: 'eskiz' as const,
      apiKey,
      apiUrl: 'https://notify.eskiz.uz',
      from: '4546',
    },
    sms_uz: {
      provider: 'sms_uz' as const,
      apiKey,
      apiUrl: 'https://api.sms.uz',
      from: 'SMS.UZ',
    },
    playmobile: {
      provider: 'playmobile' as const,
      apiKey,
      apiUrl: 'https://send.playmobile.uz',
    },
  }

  return new SMSService(configs[provider])
}

export { SMSService }
export type { SMSMessage, SMSResponse, SMSConfig }
