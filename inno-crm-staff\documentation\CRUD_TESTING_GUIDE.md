# CRUD Operations Testing Guide

## Overview
This guide provides comprehensive testing instructions for all CRUD (Create, Read, Update, Delete) operations implemented in the CRM system.

## Prerequisites
1. Ensure the development server is running: `npm run dev`
2. Database is properly seeded with initial data
3. All environment variables are configured
4. <PERSON><PERSON><PERSON> is open to `http://localhost:3001`

## Testing Checklist

### 1. Students Management (`/dashboard/students`)

#### Create Student
- [ ] Click "Add Student" button
- [ ] Verify modal dialog opens with student form
- [ ] Fill in required fields:
  - [ ] Name (required)
  - [ ] Phone (required, unique)
  - [ ] Email (optional)
  - [ ] Level (dropdown selection)
  - [ ] Branch (required)
  - [ ] Emergency Contact (optional)
  - [ ] Date of Birth (optional)
  - [ ] Address (optional)
- [ ] Submit form and verify:
  - [ ] Success message appears
  - [ ] Modal closes
  - [ ] Student appears in the table
  - [ ] User account is created automatically

#### Read Students
- [ ] Verify students table displays correctly
- [ ] Test search functionality:
  - [ ] Search by name
  - [ ] Search by phone number
  - [ ] Search by email
- [ ] Click "View Details" to see student information
- [ ] Verify all student data displays correctly

#### Update Student
- [ ] Click Edit button (pencil icon) for any student
- [ ] Verify modal opens with pre-populated data
- [ ] Modify student information
- [ ] Submit changes and verify:
  - [ ] Success feedback
  - [ ] Updated data appears in table
  - [ ] Modal closes

#### Delete Student
- [ ] Click Delete button (trash icon) for a student
- [ ] Verify confirmation dialog appears
- [ ] Confirm deletion and verify:
  - [ ] Student is removed from table
  - [ ] Success feedback (if implemented)
- [ ] Test deletion prevention for students with active enrollments

### 2. Courses Management (`/dashboard/courses`)

#### Create Course
- [ ] Click "Add Course" button
- [ ] Fill in course form:
  - [ ] Course Name (required, unique)
  - [ ] Level (dropdown selection)
  - [ ] Description (optional)
  - [ ] Duration in weeks (required, 1-52)
  - [ ] Price (required, non-negative)
  - [ ] Active status (checkbox)
- [ ] Submit and verify course appears in table

#### Read Courses
- [ ] Verify courses table displays correctly
- [ ] Test search functionality
- [ ] Verify course statistics cards update correctly
- [ ] Check level badges display properly

#### Update Course
- [ ] Click Edit button for any course
- [ ] Modify course details
- [ ] Submit changes and verify updates

#### Delete Course
- [ ] Try to delete course with groups (should be prevented)
- [ ] Delete course without groups (should succeed)
- [ ] Verify proper error messages for restricted deletions

### 3. Groups Management (`/dashboard/groups`)

#### Create Group
- [ ] Click "Create Group" button
- [ ] Fill in group form:
  - [ ] Group Name (required, unique)
  - [ ] Course Selection (dropdown)
  - [ ] Teacher Selection (dropdown)
  - [ ] Capacity (1-50)
  - [ ] Schedule (required)
  - [ ] Room (optional)
  - [ ] Branch (required)
  - [ ] Start Date (required)
  - [ ] End Date (required)
  - [ ] Active status
- [ ] Submit and verify group appears in grid

#### Read Groups
- [ ] Verify groups display in card grid format
- [ ] Test search functionality
- [ ] Verify group statistics are accurate
- [ ] Check capacity indicators (current/max)

#### Update Group
- [ ] Click Edit button on any group card
- [ ] Modify group details
- [ ] Submit changes and verify updates

#### Delete Group
- [ ] Try to delete group with enrollments (should be prevented)
- [ ] Delete empty group (should succeed)
- [ ] Verify proper error handling

### 4. Enrollments Management (`/dashboard/enrollments`)

#### Create Enrollment
- [ ] Click "New Enrollment" button
- [ ] Select student from dropdown
- [ ] Select group from dropdown
- [ ] Verify level matching warnings (if applicable)
- [ ] Set enrollment status
- [ ] Set start date
- [ ] Submit and verify enrollment appears in table

#### Read Enrollments
- [ ] Verify enrollments table displays correctly
- [ ] Test search functionality across all fields
- [ ] Test status filter dropdown
- [ ] Verify enrollment statistics cards

#### Update Enrollment
- [ ] Click Edit button for any enrollment
- [ ] Modify enrollment status
- [ ] Update dates if needed
- [ ] Submit changes and verify updates

#### Delete Enrollment
- [ ] Try to delete active enrollment (may be restricted)
- [ ] Delete completed/dropped enrollment
- [ ] Verify proper restrictions and confirmations

## Error Scenarios to Test

### Network Errors
- [ ] Disconnect internet and try CRUD operations
- [ ] Verify proper error messages display
- [ ] Verify loading states work correctly

### Validation Errors
- [ ] Submit forms with missing required fields
- [ ] Enter invalid data (negative numbers, invalid emails)
- [ ] Try to create duplicates where uniqueness is required
- [ ] Verify client-side validation works

### Server Errors
- [ ] Test with invalid API responses
- [ ] Verify error handling for 400/500 status codes
- [ ] Check that user gets meaningful error messages

### Concurrent Operations
- [ ] Open multiple browser tabs
- [ ] Perform operations in different tabs
- [ ] Verify data consistency

## Performance Testing

### Large Datasets
- [ ] Test with 100+ students
- [ ] Test with 50+ courses
- [ ] Test with 200+ enrollments
- [ ] Verify search performance
- [ ] Check loading times

### Form Performance
- [ ] Test form submission with slow network
- [ ] Verify loading states during operations
- [ ] Check for memory leaks with repeated operations

## Browser Compatibility
- [ ] Test in Chrome
- [ ] Test in Firefox
- [ ] Test in Safari
- [ ] Test in Edge
- [ ] Verify responsive design on mobile

## Accessibility Testing
- [ ] Navigate using keyboard only
- [ ] Test with screen reader
- [ ] Verify proper ARIA labels
- [ ] Check color contrast
- [ ] Test focus management in modals

## Data Integrity Testing
- [ ] Verify foreign key relationships are maintained
- [ ] Test cascade deletions work properly
- [ ] Check that orphaned records are prevented
- [ ] Verify data validation on both client and server

## Security Testing
- [ ] Test role-based access (if implemented)
- [ ] Verify input sanitization
- [ ] Test for SQL injection vulnerabilities
- [ ] Check CSRF protection

## Reporting Issues
When reporting bugs, include:
1. Steps to reproduce
2. Expected behavior
3. Actual behavior
4. Browser and version
5. Screenshots/videos if applicable
6. Console errors (if any)

## Success Criteria
All CRUD operations should:
- ✅ Work without errors
- ✅ Provide proper user feedback
- ✅ Maintain data integrity
- ✅ Handle errors gracefully
- ✅ Be responsive and performant
- ✅ Follow consistent UI patterns
