'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, UserPlus, User, GraduationCap, Calendar, Users, BookOpen, DollarSign } from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'

const enrollmentSchema = z.object({
  studentId: z.string().min(1, 'Student is required'),
  groupId: z.string().min(1, 'Group is required'),
  status: z.enum(['ACTIVE', 'COMPLETED', 'DROPPED', 'SUSPENDED']).default('ACTIVE'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
})

type EnrollmentFormData = z.infer<typeof enrollmentSchema>

interface EnrollmentFormProps {
  initialData?: Partial<EnrollmentFormData>
  onSubmit: (data: EnrollmentFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
  preselectedStudentId?: string
  preselectedGroupId?: string
}

interface Student {
  id: string
  level: string
  branch: string
  user: {
    id: string
    name: string
    phone: string
  }
  enrollments: Array<{
    status: string
    group: {
      name: string
      course: {
        name: string
        level: string
      }
    }
  }>
}

interface Group {
  id: string
  name: string
  capacity: number
  branch: string
  startDate: string
  endDate: string
  isActive: boolean
  course: {
    id: string
    name: string
    level: string
    duration: number
    price: number
  }
  teacher: {
    user: {
      name: string
    }
  }
  _count: {
    enrollments: number
  }
}

const enrollmentStatuses = [
  { value: 'ACTIVE', label: 'Active', color: 'bg-green-100 text-green-800' },
  { value: 'COMPLETED', label: 'Completed', color: 'bg-blue-100 text-blue-800' },
  { value: 'DROPPED', label: 'Dropped', color: 'bg-red-100 text-red-800' },
  { value: 'SUSPENDED', label: 'Suspended', color: 'bg-yellow-100 text-yellow-800' },
]

function EnrollmentForm({
  initialData,
  onSubmit,
  onCancel,
  isEditing = false,
  preselectedStudentId,
  preselectedGroupId
}: EnrollmentFormProps) {
  const { currentBranch } = useBranch()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [students, setStudents] = useState<Student[]>([])
  const [groups, setGroups] = useState<Group[]>([])
  const [filteredGroups, setFilteredGroups] = useState<Group[]>([])
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<EnrollmentFormData>({
    resolver: zodResolver(enrollmentSchema),
    defaultValues: {
      studentId: preselectedStudentId || initialData?.studentId || '',
      groupId: preselectedGroupId || initialData?.groupId || '',
      status: initialData?.status || 'ACTIVE',
      startDate: initialData?.startDate || new Date().toISOString().split('T')[0],
      endDate: initialData?.endDate || '',
    },
  })

  const selectedStudentId = watch('studentId')
  const selectedGroupId = watch('groupId')
  const selectedStatus = watch('status')

  useEffect(() => {
    if (currentBranch?.id) {
      fetchStudents()
      fetchGroups()
    }
  }, [currentBranch?.id])

  useEffect(() => {
    if (selectedStudentId) {
      const student = students.find(s => s.id === selectedStudentId)
      setSelectedStudent(student || null)
      
      // Filter groups by student's level and branch
      if (student) {
        const filtered = groups.filter(group => 
          group.isActive && 
          group.branch === student.branch &&
          group.course.level === student.level &&
          group._count.enrollments < group.capacity
        )
        setFilteredGroups(filtered)
      }
    } else {
      setFilteredGroups(groups.filter(g => g.isActive))
    }
  }, [selectedStudentId, students, groups])

  useEffect(() => {
    if (selectedGroupId) {
      const group = groups.find(g => g.id === selectedGroupId)
      setSelectedGroup(group || null)
      
      // Auto-set end date based on group's end date
      if (group && !watch('endDate')) {
        setValue('endDate', group.endDate)
      }
    }
  }, [selectedGroupId, groups, setValue, watch])

  const fetchStudents = async () => {
    if (!currentBranch?.id) return

    try {
      const response = await fetch(`/api/students?branch=${currentBranch.id}`)
      const data = await response.json()
      setStudents(data.students || [])
    } catch (error) {
      console.error('Error fetching students:', error)
    }
  }

  const fetchGroups = async () => {
    if (!currentBranch?.id) return

    try {
      const response = await fetch(`/api/groups?branch=${currentBranch.id}`)
      const data = await response.json()
      setGroups(data.groups || [])
    } catch (error) {
      console.error('Error fetching groups:', error)
    }
  }

  const handleFormSubmit = async (data: EnrollmentFormData) => {
    setIsSubmitting(true)
    setError(null)

    try {
      await onSubmit(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const isGroupFull = Boolean(selectedGroup && selectedGroup._count.enrollments >= selectedGroup.capacity)
  const hasConflictingEnrollment = selectedStudent?.enrollments.some(
    enrollment => enrollment.status === 'ACTIVE' && 
    enrollment.group.course.level === selectedGroup?.course.level
  )

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <UserPlus className="h-5 w-5 mr-2" />
          {isEditing ? 'Edit Enrollment' : 'New Student Enrollment'}
        </CardTitle>
        <CardDescription>
          {isEditing ? 'Update enrollment information' : 'Enroll a student in a group'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Student Selection */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <User className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Student Information</h3>
            </div>

            <div className="space-y-2">
              <Label htmlFor="studentId">Student *</Label>
              <Select
                value={selectedStudentId}
                onValueChange={(value) => setValue('studentId', value)}
                disabled={!!preselectedStudentId}
              >
                <SelectTrigger className={errors.studentId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select student" />
                </SelectTrigger>
                <SelectContent>
                  {students.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.user.name} - {student.level} ({student.branch})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.studentId && (
                <p className="text-sm text-red-500">{errors.studentId.message}</p>
              )}
              
              {selectedStudent && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{selectedStudent.user.name}</p>
                      <p className="text-sm text-gray-600">{selectedStudent.user.phone}</p>
                      <div className="flex space-x-2 mt-2">
                        <Badge variant="outline">{selectedStudent.level}</Badge>
                        <Badge variant="outline">{selectedStudent.branch}</Badge>
                      </div>
                    </div>
                  </div>
                  
                  {selectedStudent.enrollments.length > 0 && (
                    <div className="mt-3 pt-3 border-t">
                      <p className="text-sm font-medium text-gray-700">Current Enrollments:</p>
                      {selectedStudent.enrollments.map((enrollment, index) => (
                        <div key={index} className="text-sm text-gray-600 mt-1">
                          {enrollment.group.name} - {enrollment.group.course.name} 
                          <Badge className="ml-2" size="sm">
                            {enrollment.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Group Selection */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <GraduationCap className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Group Selection</h3>
            </div>

            <div className="space-y-2">
              <Label htmlFor="groupId">Group *</Label>
              <Select
                value={selectedGroupId}
                onValueChange={(value) => setValue('groupId', value)}
                disabled={!!preselectedGroupId}
              >
                <SelectTrigger className={errors.groupId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select group" />
                </SelectTrigger>
                <SelectContent>
                  {filteredGroups.map((group) => (
                    <SelectItem key={group.id} value={group.id}>
                      {group.name} - {group.course.name} ({group._count.enrollments}/{group.capacity})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.groupId && (
                <p className="text-sm text-red-500">{errors.groupId.message}</p>
              )}
              
              {selectedStudent && filteredGroups.length === 0 && (
                <Alert>
                  <AlertDescription>
                    No available groups found for this student&apos;s level ({selectedStudent.level}) and branch ({selectedStudent.branch}).
                  </AlertDescription>
                </Alert>
              )}

              {selectedGroup && (
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="font-medium">{selectedGroup.name}</p>
                      <p className="text-sm text-gray-600">{selectedGroup.course.name}</p>
                      <p className="text-sm text-gray-600">Teacher: {selectedGroup.teacher.user.name}</p>
                    </div>
                    <div className="text-right">
                      <Badge className={selectedGroup.course.level === selectedStudent?.level ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                        {selectedGroup.course.level}
                      </Badge>
                      <p className="text-sm text-gray-600 mt-1">
                        {selectedGroup._count.enrollments}/{selectedGroup.capacity} students
                      </p>
                      <p className="text-sm font-medium text-gray-900">
                        ${(selectedGroup.course.price / 12500).toFixed(0)} / month
                      </p>
                    </div>
                  </div>
                  
                  <div className="mt-3 pt-3 border-t text-sm text-gray-600">
                    <p>Duration: {selectedGroup.course.duration} weeks</p>
                    <p>Period: {new Date(selectedGroup.startDate).toLocaleDateString()} - {new Date(selectedGroup.endDate).toLocaleDateString()}</p>
                  </div>

                  {isGroupFull && (
                    <Alert className="mt-3" variant="destructive">
                      <AlertDescription>
                        This group is at full capacity ({selectedGroup.capacity} students).
                      </AlertDescription>
                    </Alert>
                  )}

                  {hasConflictingEnrollment && (
                    <Alert className="mt-3">
                      <AlertDescription>
                        Student is already enrolled in another {selectedGroup.course.level} level course.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Enrollment Details */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Calendar className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Enrollment Details</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={selectedStatus}
                  onValueChange={(value) => setValue('status', value as any)}
                >
                  <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {enrollmentStatuses.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-500">{errors.status.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date *</Label>
                <Input
                  id="startDate"
                  type="date"
                  {...register('startDate')}
                  className={errors.startDate ? 'border-red-500' : ''}
                />
                {errors.startDate && (
                  <p className="text-sm text-red-500">{errors.startDate.message}</p>
                )}
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  {...register('endDate')}
                />
                <p className="text-sm text-gray-600">
                  Leave empty to use group&apos;s end date
                </p>
              </div>
            </div>
          </div>

          {/* Enrollment Summary */}
          {selectedStudent && selectedGroup && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Enrollment Summary</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Student:</span>
                  <span className="ml-2 font-medium">{selectedStudent.user.name}</span>
                </div>
                <div>
                  <span className="text-gray-600">Group:</span>
                  <span className="ml-2 font-medium">{selectedGroup.name}</span>
                </div>
                <div>
                  <span className="text-gray-600">Course:</span>
                  <span className="ml-2 font-medium">{selectedGroup.course.name}</span>
                </div>
                <div>
                  <span className="text-gray-600">Level Match:</span>
                  <span className={`ml-2 font-medium ${selectedGroup.course.level === selectedStudent.level ? 'text-green-600' : 'text-yellow-600'}`}>
                    {selectedGroup.course.level === selectedStudent.level ? 'Perfect Match' : 'Level Mismatch'}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button 
              type="submit" 
              disabled={isSubmitting || isGroupFull}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update Enrollment' : 'Enroll Student'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default EnrollmentForm
export { EnrollmentForm }
