'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Loader2, UserCheck, Calendar, Users, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react'

const attendanceSchema = z.object({
  classId: z.string().min(1, 'Class is required'),
  attendances: z.array(z.object({
    studentId: z.string(),
    status: z.enum(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED']),
    notes: z.string().optional(),
  })).min(1, 'At least one student attendance is required'),
})

type AttendanceFormData = z.infer<typeof attendanceSchema>

interface AttendanceFormProps {
  initialData?: Partial<AttendanceFormData>
  onSubmit: (data: AttendanceFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
  preselectedClassId?: string
}

interface Class {
  id: string
  date: string
  topic: string | null
  group: {
    id: string
    name: string
    course: {
      name: string
      level: string
    }
    enrollments: Array<{
      student: {
        id: string
        user: {
          name: string
          phone: string
        }
      }
    }>
  }
  teacher: {
    user: {
      name: string
    }
  }
  attendances?: Array<{
    studentId: string
    status: string
    notes: string | null
  }>
}

interface StudentAttendance {
  studentId: string
  studentName: string
  studentPhone: string
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED'
  notes: string
}

const attendanceStatuses = [
  { value: 'PRESENT', label: 'Present', icon: CheckCircle, color: 'text-green-600' },
  { value: 'ABSENT', label: 'Absent', icon: XCircle, color: 'text-red-600' },
  { value: 'LATE', label: 'Late', icon: Clock, color: 'text-yellow-600' },
  { value: 'EXCUSED', label: 'Excused', icon: AlertCircle, color: 'text-blue-600' },
]

function AttendanceForm({
  initialData,
  onSubmit,
  onCancel,
  isEditing = false,
  preselectedClassId
}: AttendanceFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [classes, setClasses] = useState<Class[]>([])
  const [selectedClass, setSelectedClass] = useState<Class | null>(null)
  const [studentAttendances, setStudentAttendances] = useState<StudentAttendance[]>([])

  const {
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<AttendanceFormData>({
    resolver: zodResolver(attendanceSchema),
    defaultValues: {
      classId: preselectedClassId || initialData?.classId || '',
      attendances: initialData?.attendances || [],
    },
  })

  const selectedClassId = watch('classId')

  useEffect(() => {
    fetchClasses()
  }, [])

  useEffect(() => {
    if (selectedClassId) {
      const classData = classes.find(c => c.id === selectedClassId)
      setSelectedClass(classData || null)
      
      if (classData) {
        // Initialize student attendances
        const attendances: StudentAttendance[] = classData.group.enrollments.map(enrollment => {
          const existingAttendance = classData.attendances?.find(a => a.studentId === enrollment.student.id)
          
          return {
            studentId: enrollment.student.id,
            studentName: enrollment.student.user.name,
            studentPhone: enrollment.student.user.phone,
            status: (existingAttendance?.status as any) || 'PRESENT',
            notes: existingAttendance?.notes || '',
          }
        })
        
        setStudentAttendances(attendances)
        setValue('attendances', attendances.map(a => ({
          studentId: a.studentId,
          status: a.status,
          notes: a.notes,
        })))
      }
    }
  }, [selectedClassId, classes, setValue])

  const fetchClasses = async () => {
    try {
      // Since we removed classes functionality, we'll fetch groups instead
      // This is a temporary solution - in a real implementation, you might want to
      // create a different attendance tracking system
      const response = await fetch('/api/groups')
      const data = await response.json()

      // Convert groups to a class-like structure for compatibility
      const groupsAsClasses = data.groups?.map((group: any) => ({
        id: group.id,
        date: new Date().toISOString(), // Current date as placeholder
        topic: `${group.course.name} - ${group.course.level}`,
        group: {
          id: group.id,
          name: group.name,
          course: group.course,
          enrollments: group.enrollments || []
        },
        teacher: group.teacher,
        attendances: []
      })) || []

      setClasses(groupsAsClasses)
    } catch (error) {
      console.error('Error fetching groups for attendance:', error)
    }
  }

  const handleFormSubmit = async (data: AttendanceFormData) => {
    setIsSubmitting(true)
    setError(null)

    try {
      await onSubmit(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const updateStudentAttendance = (studentId: string, field: 'status' | 'notes', value: string) => {
    const updatedAttendances = studentAttendances.map(attendance =>
      attendance.studentId === studentId
        ? { ...attendance, [field]: value }
        : attendance
    )
    
    setStudentAttendances(updatedAttendances)
    setValue('attendances', updatedAttendances.map(a => ({
      studentId: a.studentId,
      status: a.status,
      notes: a.notes,
    })))
  }

  const markAllAs = (status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED') => {
    const updatedAttendances = studentAttendances.map(attendance => ({
      ...attendance,
      status,
    }))
    
    setStudentAttendances(updatedAttendances)
    setValue('attendances', updatedAttendances.map(a => ({
      studentId: a.studentId,
      status: a.status,
      notes: a.notes,
    })))
  }

  const getStatusIcon = (status: string) => {
    const statusInfo = attendanceStatuses.find(s => s.value === status)
    if (!statusInfo) return null
    
    const Icon = statusInfo.icon
    return <Icon className={`h-4 w-4 ${statusInfo.color}`} />
  }

  // Calculate statistics
  const totalStudents = studentAttendances.length
  const presentCount = studentAttendances.filter(a => a.status === 'PRESENT').length
  const absentCount = studentAttendances.filter(a => a.status === 'ABSENT').length
  const lateCount = studentAttendances.filter(a => a.status === 'LATE').length
  const excusedCount = studentAttendances.filter(a => a.status === 'EXCUSED').length
  const attendanceRate = totalStudents > 0 ? ((presentCount + lateCount) / totalStudents) * 100 : 0

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <UserCheck className="h-5 w-5 mr-2" />
          {isEditing ? 'Edit Attendance' : 'Mark Class Attendance'}
        </CardTitle>
        <CardDescription>
          {isEditing ? 'Update attendance records' : 'Mark attendance for students in the selected class'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Class Selection */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Calendar className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Class Information</h3>
            </div>

            <div className="space-y-2">
              <Label htmlFor="classId">Select Class *</Label>
              <Select
                value={selectedClassId}
                onValueChange={(value) => setValue('classId', value)}
                disabled={!!preselectedClassId}
              >
                <SelectTrigger className={errors.classId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map((classItem) => (
                    <SelectItem key={classItem.id} value={classItem.id}>
                      {classItem.group.name} - {new Date(classItem.date).toLocaleDateString()} 
                      {classItem.topic && ` - ${classItem.topic}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.classId && (
                <p className="text-sm text-red-500">{errors.classId.message}</p>
              )}

              {selectedClass && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="font-medium">{selectedClass.group.name}</p>
                      <p className="text-sm text-gray-600">{selectedClass.group.course.name}</p>
                      <p className="text-sm text-gray-600">Teacher: {selectedClass.teacher.user.name}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{new Date(selectedClass.date).toLocaleDateString()}</p>
                      <Badge variant="outline">{selectedClass.group.course.level}</Badge>
                      <p className="text-sm text-gray-600 mt-1">
                        {selectedClass.group.enrollments.length} students
                      </p>
                    </div>
                  </div>
                  {selectedClass.topic && (
                    <div className="mt-3 pt-3 border-t">
                      <p className="text-sm"><strong>Topic:</strong> {selectedClass.topic}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Attendance Statistics */}
          {studentAttendances.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <Users className="h-6 w-6 mx-auto text-blue-600 mb-2" />
                  <p className="text-2xl font-bold">{totalStudents}</p>
                  <p className="text-sm text-gray-600">Total</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <CheckCircle className="h-6 w-6 mx-auto text-green-600 mb-2" />
                  <p className="text-2xl font-bold">{presentCount}</p>
                  <p className="text-sm text-gray-600">Present</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <XCircle className="h-6 w-6 mx-auto text-red-600 mb-2" />
                  <p className="text-2xl font-bold">{absentCount}</p>
                  <p className="text-sm text-gray-600">Absent</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <Clock className="h-6 w-6 mx-auto text-yellow-600 mb-2" />
                  <p className="text-2xl font-bold">{lateCount}</p>
                  <p className="text-sm text-gray-600">Late</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">{attendanceRate.toFixed(1)}%</p>
                    <p className="text-sm text-gray-600">Rate</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Bulk Actions */}
          {studentAttendances.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 mb-4">
                <UserCheck className="h-4 w-4 text-gray-500" />
                <h3 className="text-lg font-medium">Quick Actions</h3>
              </div>
              
              <div className="flex flex-wrap gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => markAllAs('PRESENT')}
                >
                  <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                  Mark All Present
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => markAllAs('ABSENT')}
                >
                  <XCircle className="h-4 w-4 mr-2 text-red-600" />
                  Mark All Absent
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => markAllAs('LATE')}
                >
                  <Clock className="h-4 w-4 mr-2 text-yellow-600" />
                  Mark All Late
                </Button>
              </div>
            </div>
          )}

          {/* Student Attendance List */}
          {studentAttendances.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 mb-4">
                <Users className="h-4 w-4 text-gray-500" />
                <h3 className="text-lg font-medium">Student Attendance</h3>
              </div>

              <div className="space-y-3">
                {studentAttendances.map((attendance) => (
                  <Card key={attendance.studentId}>
                    <CardContent className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                        <div>
                          <p className="font-medium">{attendance.studentName}</p>
                          <p className="text-sm text-gray-600">{attendance.studentPhone}</p>
                        </div>
                        
                        <div className="flex space-x-2">
                          {attendanceStatuses.map((status) => (
                            <Button
                              key={status.value}
                              type="button"
                              variant={attendance.status === status.value ? "default" : "outline"}
                              size="sm"
                              onClick={() => updateStudentAttendance(attendance.studentId, 'status', status.value)}
                              className={attendance.status === status.value ? '' : 'hover:bg-gray-50'}
                            >
                              {getStatusIcon(status.value)}
                              <span className="ml-1 hidden sm:inline">{status.label}</span>
                            </Button>
                          ))}
                        </div>
                        
                        <div className="md:col-span-2">
                          <Input
                            placeholder="Notes (optional)"
                            value={attendance.notes}
                            onChange={(e) => updateStudentAttendance(attendance.studentId, 'notes', e.target.value)}
                            className="text-sm"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {errors.attendances && (
            <Alert variant="destructive">
              <AlertDescription>{errors.attendances.message}</AlertDescription>
            </Alert>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button 
              type="submit" 
              disabled={isSubmitting || studentAttendances.length === 0}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update Attendance' : 'Save Attendance'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default AttendanceForm
export { AttendanceForm }
