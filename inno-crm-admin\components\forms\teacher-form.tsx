'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, User, Phone, Mail, BookOpen, Award, MapPin } from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'

const teacherSchema = z.object({
  // User information (for new teachers)
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: z.string().min(9, 'Phone number must be at least 9 characters').optional(),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),

  // Teacher specific information
  userId: z.string().optional(), // For existing users
  subject: z.string().min(1, 'Subject is required'),
  experience: z.number().min(0, 'Experience cannot be negative').optional(),
  branch: z.string().min(1, 'Branch is required'),
  tier: z.enum(['A_LEVEL', 'B_LEVEL', 'C_LEVEL', 'NEW']).default('NEW'),
})

type TeacherFormData = z.infer<typeof teacherSchema>

interface TeacherFormProps {
  initialData?: Partial<TeacherFormData>
  onSubmit: (data: TeacherFormData) => Promise<void>
  onCancel?: () => void
  isEditing?: boolean
  existingUsers?: Array<{ id: string; name: string; phone: string; email?: string }>
}

const subjects = [
  'English Language',
  'IELTS Preparation',
  'SAT Preparation',
  'Mathematics',
  'Kids English',
  'Business English',
  'Academic English',
  'Conversation English',
  'Grammar & Writing',
  'Speaking & Listening',
]

const branches = [
  'Main Branch',
  'Branch',
]

const teacherTiers = [
  { value: 'A_LEVEL', label: 'A-level Teacher', description: 'Highest priority, experienced teacher' },
  { value: 'B_LEVEL', label: 'B-level Teacher', description: 'Experienced teacher' },
  { value: 'C_LEVEL', label: 'C-level Teacher', description: 'Developing teacher' },
  { value: 'NEW', label: 'New Teacher', description: 'New or trainee teacher' },
]

export default function TeacherForm({
  initialData,
  onSubmit,
  onCancel,
  isEditing = false,
  existingUsers = []
}: TeacherFormProps) {
  const { currentBranch } = useBranch()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [useExistingUser, setUseExistingUser] = useState(!!initialData?.userId)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<TeacherFormData>({
    resolver: zodResolver(teacherSchema),
    defaultValues: {
      name: initialData?.name || '',
      phone: initialData?.phone || '',
      email: initialData?.email || '',
      userId: initialData?.userId || '',
      subject: initialData?.subject || '',
      experience: initialData?.experience || 0,
      branch: initialData?.branch || currentBranch.name,
      tier: initialData?.tier || 'NEW',
    },
  })

  // Set default branch when component mounts or currentBranch changes
  useEffect(() => {
    if (!initialData?.branch && currentBranch?.name) {
      setValue('branch', currentBranch.name)
    }
  }, [currentBranch, initialData?.branch, setValue])

  const selectedUserId = watch('userId')
  const selectedSubject = watch('subject')
  const selectedBranch = watch('branch')
  const selectedTier = watch('tier')

  const handleFormSubmit = async (data: TeacherFormData) => {
    setIsSubmitting(true)
    setError(null)

    try {
      await onSubmit(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <User className="h-5 w-5 mr-2" />
          {isEditing ? 'Edit Teacher' : 'Add New Teacher'}
        </CardTitle>
        <CardDescription>
          {isEditing ? 'Update teacher information' : 'Enter teacher details to create a new profile'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* User Selection/Creation */}
          {!isEditing && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 mb-4">
                <User className="h-4 w-4 text-gray-500" />
                <h3 className="text-lg font-medium">User Account</h3>
              </div>

              <div className="flex space-x-4 mb-4">
                <Button
                  type="button"
                  variant={useExistingUser ? "outline" : "default"}
                  onClick={() => setUseExistingUser(false)}
                >
                  Create New User
                </Button>
                <Button
                  type="button"
                  variant={useExistingUser ? "default" : "outline"}
                  onClick={() => setUseExistingUser(true)}
                >
                  Use Existing User
                </Button>
              </div>

              {useExistingUser ? (
                <div className="space-y-2">
                  <Label htmlFor="userId">Select User *</Label>
                  <Select
                    value={selectedUserId}
                    onValueChange={(value) => setValue('userId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select an existing user" />
                    </SelectTrigger>
                    <SelectContent>
                      {existingUsers.map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.name} - {user.phone}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      {...register('name')}
                      placeholder="Enter full name"
                      className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-500">{errors.name.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="phone"
                        {...register('phone')}
                        placeholder="+998 90 123 45 67"
                        className={`pl-10 ${errors.phone ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {errors.phone && (
                      <p className="text-sm text-red-500">{errors.phone.message}</p>
                    )}
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="email">Email Address</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        {...register('email')}
                        placeholder="<EMAIL>"
                        className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                      />
                    </div>
                    {errors.email && (
                      <p className="text-sm text-red-500">{errors.email.message}</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Professional Information Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <BookOpen className="h-4 w-4 text-gray-500" />
              <h3 className="text-lg font-medium">Professional Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="subject">Subject/Specialization *</Label>
                <Select
                  value={selectedSubject}
                  onValueChange={(value) => setValue('subject', value)}
                >
                  <SelectTrigger className={errors.subject ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select subject" />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map((subject) => (
                      <SelectItem key={subject} value={subject}>
                        {subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.subject && (
                  <p className="text-sm text-red-500">{errors.subject.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="branch">Branch *</Label>
                <Select
                  value={selectedBranch}
                  onValueChange={(value) => setValue('branch', value)}
                >
                  <SelectTrigger className={errors.branch ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem key={branch} value={branch}>
                        {branch}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.branch && (
                  <p className="text-sm text-red-500">{errors.branch.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="experience">Years of Experience</Label>
                <div className="relative">
                  <Award className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="experience"
                    type="number"
                    min="0"
                    step="0.5"
                    {...register('experience', { valueAsNumber: true })}
                    placeholder="0"
                    className={`pl-10 ${errors.experience ? 'border-red-500' : ''}`}
                  />
                </div>
                {errors.experience && (
                  <p className="text-sm text-red-500">{errors.experience.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="tier">Teacher Tier *</Label>
                <Select
                  value={selectedTier}
                  onValueChange={(value) => setValue('tier', value as any)}
                >
                  <SelectTrigger className={errors.tier ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select tier" />
                  </SelectTrigger>
                  <SelectContent>
                    {teacherTiers.map((tier) => (
                      <SelectItem key={tier.value} value={tier.value}>
                        <div className="flex flex-col">
                          <span className="font-medium">{tier.label}</span>
                          <span className="text-xs text-gray-500">{tier.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.tier && (
                  <p className="text-sm text-red-500">{errors.tier.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update Teacher' : 'Create Teacher'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
