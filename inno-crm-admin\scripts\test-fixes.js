const fs = require('fs')
const path = require('path')

console.log('🧪 Testing Notification System & Database Fixes')
console.log('=' .repeat(50))

// Test 1: Check if notification component exists
console.log('\n📱 Testing Notification System...')

const notificationDropdownPath = 'components/notifications/notification-dropdown.tsx'
if (fs.existsSync(notificationDropdownPath)) {
  console.log('✅ Notification dropdown component exists')
  
  const content = fs.readFileSync(notificationDropdownPath, 'utf8')
  
  // Check for key features
  const features = [
    { name: 'Bell icon import', pattern: /Bell.*from.*lucide-react/ },
    { name: 'Unread count state', pattern: /unreadCount.*useState/ },
    { name: 'Mark as read function', pattern: /markAsRead/ },
    { name: 'Notification types', pattern: /success.*warning.*error.*info/ },
    { name: 'Date formatting', pattern: /formatDistanceToNow/ },
    { name: 'API integration', pattern: /fetch.*notifications/ }
  ]
  
  features.forEach(feature => {
    if (feature.pattern.test(content)) {
      console.log(`  ✅ ${feature.name}`)
    } else {
      console.log(`  ❌ ${feature.name} - Missing`)
    }
  })
} else {
  console.log('❌ Notification dropdown component missing')
}

// Test 2: Check if header is updated
console.log('\n📋 Testing Header Integration...')

const headerPath = 'components/dashboard/header.tsx'
if (fs.existsSync(headerPath)) {
  const content = fs.readFileSync(headerPath, 'utf8')
  
  if (content.includes('NotificationDropdown')) {
    console.log('✅ Header uses NotificationDropdown component')
  } else {
    console.log('❌ Header not updated to use NotificationDropdown')
  }
  
  if (!content.includes('Bell.*className.*h-5 w-5')) {
    console.log('✅ Old hardcoded bell icon removed')
  } else {
    console.log('❌ Old hardcoded bell icon still present')
  }
} else {
  console.log('❌ Header component missing')
}

// Test 3: Check notification test API
console.log('\n🔌 Testing Notification API...')

const notificationTestApiPath = 'app/api/notifications/test/route.ts'
if (fs.existsSync(notificationTestApiPath)) {
  console.log('✅ Notification test API exists')
  
  const content = fs.readFileSync(notificationTestApiPath, 'utf8')
  
  const apiFeatures = [
    { name: 'GET endpoint', pattern: /export.*async.*function.*GET/ },
    { name: 'POST endpoint', pattern: /export.*async.*function.*POST/ },
    { name: 'Mock notifications', pattern: /mockNotifications/ },
    { name: 'Status endpoint', pattern: /action.*===.*status/ },
    { name: 'Mock action', pattern: /action.*===.*mock/ }
  ]
  
  apiFeatures.forEach(feature => {
    if (feature.pattern.test(content)) {
      console.log(`  ✅ ${feature.name}`)
    } else {
      console.log(`  ❌ ${feature.name} - Missing`)
    }
  })
} else {
  console.log('❌ Notification test API missing')
}

// Test 4: Check database fixes
console.log('\n🗄️  Testing Database Fixes...')

const studentsApiPath = 'app/api/students/route.ts'
if (fs.existsSync(studentsApiPath)) {
  const content = fs.readFileSync(studentsApiPath, 'utf8')
  
  if (content.includes('try {') && content.includes('statusCounts = await prisma.student.groupBy')) {
    console.log('✅ Students API has graceful error handling for status column')
  } else {
    console.log('❌ Students API missing graceful error handling')
  }
  
  if (content.includes('catch (error)') && content.includes('console.warn')) {
    console.log('✅ Proper error logging implemented')
  } else {
    console.log('❌ Error logging missing')
  }
} else {
  console.log('❌ Students API missing')
}

// Test 5: Check level enum updates
console.log('\n📊 Testing Level Enum Updates...')

const schemaPath = 'prisma/schema.prisma'
if (fs.existsSync(schemaPath)) {
  const content = fs.readFileSync(schemaPath, 'utf8')
  
  // Check for new level values
  const newLevels = ['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']
  const oldLevels = ['C1', 'C2', 'IELTS_5_5', 'IELTS_6_0', 'IELTS_6_5', 'IELTS_7_0']
  
  const hasNewLevels = newLevels.every(level => content.includes(level))
  const hasOldLevels = oldLevels.some(level => content.includes(level))
  
  if (hasNewLevels) {
    console.log('✅ Schema contains new level values')
  } else {
    console.log('❌ Schema missing new level values')
  }
  
  if (!hasOldLevels) {
    console.log('✅ Old level values removed from schema')
  } else {
    console.log('❌ Old level values still present in schema')
  }
} else {
  console.log('❌ Prisma schema missing')
}

// Test 6: Check component updates
console.log('\n🎨 Testing Component Updates...')

const componentsToCheck = [
  'components/forms/student-form.tsx',
  'components/forms/course-form.tsx',
  'app/api/courses/route.ts',
  'app/(dashboard)/dashboard/students/page.tsx'
]

let updatedComponents = 0

componentsToCheck.forEach(componentPath => {
  if (fs.existsSync(componentPath)) {
    const content = fs.readFileSync(componentPath, 'utf8')
    
    // Check if old level values are removed
    const hasOldLevels = /IELTS_[567]|C[12]/.test(content)
    
    if (!hasOldLevels) {
      console.log(`  ✅ ${path.basename(componentPath)} - Updated`)
      updatedComponents++
    } else {
      console.log(`  ❌ ${path.basename(componentPath)} - Still has old level values`)
    }
  } else {
    console.log(`  ❌ ${path.basename(componentPath)} - Missing`)
  }
})

console.log(`📊 Components updated: ${updatedComponents}/${componentsToCheck.length}`)

// Test 7: Check branch updates
console.log('\n🏢 Testing Branch Updates...')

const studentFormPath = 'components/forms/student-form.tsx'
if (fs.existsSync(studentFormPath)) {
  const content = fs.readFileSync(studentFormPath, 'utf8')
  
  if (content.includes('Main Branch') && content.includes('Branch')) {
    console.log('✅ Student form has correct branch options')
  } else {
    console.log('❌ Student form branch options not updated')
  }
  
  // Check if old branch names are removed
  const oldBranches = ['Tashkent', 'Chilanzar', 'Yunusabad']
  const hasOldBranches = oldBranches.some(branch => content.includes(branch))
  
  if (!hasOldBranches) {
    console.log('✅ Old branch names removed')
  } else {
    console.log('❌ Old branch names still present')
  }
} else {
  console.log('❌ Student form missing')
}

// Summary
console.log('\n' + '='.repeat(50))
console.log('📊 TEST SUMMARY')
console.log('='.repeat(50))

console.log('\n✅ Completed Tests:')
console.log('  📱 Notification System Components')
console.log('  📋 Header Integration')
console.log('  🔌 Notification API Endpoints')
console.log('  🗄️  Database Error Handling')
console.log('  📊 Level Enum Updates')
console.log('  🎨 Component Updates')
console.log('  🏢 Branch Option Updates')

console.log('\n🎯 Next Steps:')
console.log('  1. Start development server: npm run dev')
console.log('  2. Login to dashboard')
console.log('  3. Check notification bell in header')
console.log('  4. Try adding a student with new level/branch options')
console.log('  5. Test notification dropdown functionality')

console.log('\n📋 Manual Testing Checklist:')
console.log('  □ Notification bell shows in header')
console.log('  □ Clicking bell opens dropdown')
console.log('  □ Notifications show with proper formatting')
console.log('  □ Mark as read functionality works')
console.log('  □ Student form shows correct level options (A1, A2, B1, B2, IELTS, SAT, MATH, KIDS)')
console.log('  □ Student form shows correct branch options (Main Branch, Branch)')
console.log('  □ Adding students works without database errors')

console.log('\n🎉 All automated tests completed!')
