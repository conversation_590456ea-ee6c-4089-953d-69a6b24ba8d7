'use client'

import { useState } from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle, Trash2, Shield } from 'lucide-react'

interface User {
  id: string
  name: string
  phone: string
  email: string | null
  role: string
}

interface DeleteUserDialogProps {
  user: User | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (userId: string) => void
  isDeleting: boolean
}

export function DeleteUserDialog({ 
  user, 
  open, 
  onOpenChange, 
  onConfirm, 
  isDeleting 
}: DeleteUserDialogProps) {
  const [confirmText, setConfirmText] = useState('')
  
  if (!user) return null

  const isConfirmValid = confirmText === user.name
  const isAdminUser = user.role === 'ADMIN'

  const handleConfirm = () => {
    if (isConfirmValid && !isDeleting) {
      onConfirm(user.id)
      setConfirmText('')
    }
  }

  const handleCancel = () => {
    setConfirmText('')
    onOpenChange(false)
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <span>Delete User Account</span>
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-4">
            <div>
              You are about to permanently delete the user account for:
            </div>
            
            <div className="bg-gray-50 p-3 rounded-lg border">
              <div className="font-medium">{user.name}</div>
              <div className="text-sm text-gray-600">{user.phone}</div>
              {user.email && (
                <div className="text-sm text-gray-600">{user.email}</div>
              )}
              <div className="flex items-center space-x-1 mt-1">
                <Shield className="h-3 w-3" />
                <span className="text-sm font-medium">{user.role}</span>
              </div>
            </div>

            {isAdminUser && (
              <Alert className="border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  <strong>Warning:</strong> You are deleting an ADMIN user. This will remove all administrative privileges associated with this account.
                </AlertDescription>
              </Alert>
            )}

            <Alert className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                This action will permanently delete:
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>User account and login credentials</li>
                  <li>Associated profile data</li>
                  <li>Activity history and logs</li>
                  <li>Any related records in the system</li>
                </ul>
              </AlertDescription>
            </Alert>

            <div>
              <Label htmlFor="confirmName" className="text-sm font-medium">
                Type <strong>{user.name}</strong> to confirm deletion:
              </Label>
              <Input
                id="confirmName"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder={`Type "${user.name}" here`}
                className="mt-1"
                disabled={isDeleting}
              />
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={isDeleting}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={!isConfirmValid || isDeleting}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete User
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
