# Database Configuration - Staff Server
DATABASE_URL="postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require&channel_binding=require"

# NextAuth.js Configuration
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# SMS Service Configuration (Choose one provider)
SMS_PROVIDER="eskiz" # Options: eskiz, sms_uz, playmobile
SMS_API_KEY="your-sms-api-key"

# Eskiz.uz Configuration
ESKIZ_API_URL="https://notify.eskiz.uz"
ESKIZ_FROM="4546"

# SMS.uz Configuration
SMS_UZ_API_URL="https://api.sms.uz"
SMS_UZ_FROM="SMS.UZ"

# Playmobile Configuration
PLAYMOBILE_API_URL="https://send.playmobile.uz"
PLAYMOBILE_LOGIN="your-login"
PLAYMOBILE_PASSWORD="your-password"

# Email Service Configuration
EMAIL_PROVIDER="gmail" # Options: gmail, outlook, smtp
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="your-app-password"
EMAIL_FROM="Innovative Centre <<EMAIL>>"

# SMTP Configuration (if using custom SMTP)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"

# Application Configuration
APP_NAME="Innovative Centre"
APP_URL="http://localhost:3000"
APP_ENV="development" # Options: development, production

# File Upload Configuration
UPLOAD_MAX_SIZE="10485760" # 10MB in bytes
UPLOAD_ALLOWED_TYPES="image/jpeg,image/png,image/gif,application/pdf"

# Payment Gateway Configuration (for future integration)
PAYMENT_GATEWAY="payme" # Options: payme, click, uzcard
PAYME_MERCHANT_ID="your-payme-merchant-id"
PAYME_SECRET_KEY="your-payme-secret-key"
CLICK_MERCHANT_ID="your-click-merchant-id"
CLICK_SECRET_KEY="your-click-secret-key"

# Analytics Configuration
ANALYTICS_ENABLED="true"
ANALYTICS_RETENTION_DAYS="365"

# Cache Configuration
CACHE_TTL="300" # 5 minutes in seconds
CACHE_MAX_SIZE="1000" # Maximum number of cached items

# Rate Limiting
RATE_LIMIT_ENABLED="true"
RATE_LIMIT_MAX_REQUESTS="100"
RATE_LIMIT_WINDOW_MS="900000" # 15 minutes

# Logging Configuration
LOG_LEVEL="info" # Options: error, warn, info, debug
LOG_FILE_ENABLED="true"
LOG_FILE_PATH="./logs/app.log"

# Security Configuration
BCRYPT_ROUNDS="12"
JWT_EXPIRES_IN="7d"
SESSION_MAX_AGE="604800" # 7 days in seconds

# Backup Configuration
BACKUP_ENABLED="true"
BACKUP_SCHEDULE="0 2 * * *" # Daily at 2 AM
BACKUP_RETENTION_DAYS="30"
BACKUP_STORAGE_PATH="./backups"

# Notification Configuration
NOTIFICATION_BATCH_SIZE="50"
NOTIFICATION_RETRY_ATTEMPTS="3"
NOTIFICATION_RETRY_DELAY="5000" # 5 seconds

# Workflow Configuration
WORKFLOW_ENABLED="true"
WORKFLOW_CHECK_INTERVAL="300000" # 5 minutes
WORKFLOW_MAX_RETRIES="3"

# Development Configuration
DEBUG="false"
VERBOSE_LOGGING="false"
MOCK_SMS="false" # Set to true to mock SMS sending in development
MOCK_EMAIL="false" # Set to true to mock email sending in development

# Performance Configuration
PERFORMANCE_MONITORING="true"
SLOW_QUERY_THRESHOLD="1000" # Log queries slower than 1 second
MEMORY_USAGE_ALERT_THRESHOLD="80" # Alert when memory usage exceeds 80%

# Feature Flags
FEATURE_SMS_ENABLED="true"
FEATURE_EMAIL_ENABLED="true"
FEATURE_WORKFLOWS_ENABLED="true"
FEATURE_ANALYTICS_ENABLED="true"
FEATURE_REPORTS_ENABLED="true"
FEATURE_BULK_OPERATIONS="true"

# Uzbekistan Specific Configuration
TIMEZONE="Asia/Tashkent"
CURRENCY="UZS"
LOCALE="uz-UZ"
PHONE_COUNTRY_CODE="+998"

# Branch Configuration
DEFAULT_BRANCH="Main"
BRANCHES="Main,Chilonzor,Yunusobod"

# Course Configuration
DEFAULT_COURSE_DURATION="3" # months
DEFAULT_CLASS_DURATION="90" # minutes
MAX_STUDENTS_PER_GROUP="15"

# Payment Configuration
PAYMENT_GRACE_PERIOD="7" # days
LATE_PAYMENT_FEE="50000" # UZS
PAYMENT_REMINDER_DAYS="3,7,14" # Days before due date to send reminders

# Academic Configuration
ACADEMIC_YEAR_START="09-01" # September 1st
ACADEMIC_YEAR_END="08-31" # August 31st
SEMESTER_DURATION="4" # months
HOLIDAY_PERIODS="01-01,01-07,03-08,03-21,05-01,05-09,09-01,12-08" # National holidays

# Attendance Configuration
ATTENDANCE_GRACE_PERIOD="15" # minutes late before marked as late
ABSENCE_ALERT_THRESHOLD="3" # consecutive absences before alerting parents
ATTENDANCE_REQUIRED_PERCENTAGE="80" # minimum attendance for course completion

# Reporting Configuration
REPORT_CACHE_DURATION="3600" # 1 hour in seconds
REPORT_MAX_RECORDS="10000"
REPORT_EXPORT_FORMATS="csv,pdf,xlsx"

# Integration Configuration
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"
TELEGRAM_CHAT_ID="your-telegram-chat-id"
WHATSAPP_API_KEY="your-whatsapp-api-key"

# Monitoring Configuration
HEALTH_CHECK_ENABLED="true"
HEALTH_CHECK_INTERVAL="60000" # 1 minute
UPTIME_MONITORING="true"

# Backup and Recovery
AUTO_BACKUP_ENABLED="true"
BACKUP_ENCRYPTION_KEY="your-backup-encryption-key"
RECOVERY_EMAIL="<EMAIL>"

# Multi-language Support
DEFAULT_LANGUAGE="uz"
SUPPORTED_LANGUAGES="uz,ru,en"
TRANSLATION_API_KEY="your-translation-api-key"

# Social Media Integration
FACEBOOK_PAGE_ID="your-facebook-page-id"
INSTAGRAM_USERNAME="your-instagram-username"
YOUTUBE_CHANNEL_ID="your-youtube-channel-id"

# Customer Support
SUPPORT_EMAIL="<EMAIL>"
SUPPORT_PHONE="+998901234567"
SUPPORT_HOURS="09:00-18:00"
SUPPORT_TIMEZONE="Asia/Tashkent"
