#!/usr/bin/env node

/**
 * Deployment Preparation Script
 * This script prepares the project for Vercel deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Preparing project for Vercel deployment...\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Read package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
console.log(`📦 Project: ${packageJson.name} v${packageJson.version}`);

// Check required files
const requiredFiles = [
  'prisma/schema.prisma',
  'next.config.js',
  'vercel.json',
  '.env.production'
];

console.log('\n📋 Checking required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - Missing!`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n⚠️  Some required files are missing. Please ensure all files are present before deployment.');
}

// Check environment variables
console.log('\n🔐 Checking environment variables...');
const requiredEnvVars = [
  'DATABASE_URL',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL'
];

const envFile = fs.readFileSync('.env.production', 'utf8');
requiredEnvVars.forEach(envVar => {
  if (envFile.includes(`${envVar}=`)) {
    console.log(`✅ ${envVar}`);
  } else {
    console.log(`❌ ${envVar} - Missing in .env.production!`);
  }
});

// Check dependencies
console.log('\n📚 Checking critical dependencies...');
const criticalDeps = [
  '@prisma/client',
  'prisma',
  'next',
  'next-auth'
];

criticalDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    const version = packageJson.dependencies[dep] || packageJson.devDependencies[dep];
    console.log(`✅ ${dep}: ${version}`);
  } else {
    console.log(`❌ ${dep} - Missing!`);
  }
});

// Verify build script
console.log('\n🔧 Checking build configuration...');
if (packageJson.scripts.build && packageJson.scripts.build.includes('prisma generate')) {
  console.log('✅ Build script includes Prisma generation');
} else {
  console.log('❌ Build script missing Prisma generation');
}

if (packageJson.scripts.postinstall && packageJson.scripts.postinstall.includes('prisma generate')) {
  console.log('✅ Postinstall script includes Prisma generation');
} else {
  console.log('❌ Postinstall script missing Prisma generation');
}

// Generate deployment checklist
console.log('\n📝 Deployment Checklist:');
console.log('');
console.log('Before deploying to Vercel:');
console.log('1. ✅ Push all changes to GitHub');
console.log('2. ✅ Ensure DATABASE_URL is accessible from Vercel');
console.log('3. ✅ Set all environment variables in Vercel dashboard');
console.log('4. ✅ Verify NEXTAUTH_URL matches your Vercel domain');
console.log('5. ✅ Test database connection');
console.log('');
console.log('Environment Variables to set in Vercel:');
console.log('- DATABASE_URL');
console.log('- NEXTAUTH_SECRET');
console.log('- NEXTAUTH_URL');
console.log('- PRISMA_GENERATE_DATAPROXY=true');
console.log('- APP_ENV=production');
console.log('');
console.log('🔗 Vercel Deployment URL: https://vercel.com/new');
console.log('📖 Full guide: ./VERCEL_DEPLOYMENT_GUIDE.md');
console.log('');
console.log('🎉 Project is ready for deployment!');
