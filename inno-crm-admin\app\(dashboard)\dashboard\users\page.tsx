"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, Plus, Edit, Trash2, Download, Filter, Users, Shield, UserPlus, AlertTriangle } from "lucide-react"
import { UserForm } from "@/components/forms/user-form"
import { DeleteUserDialog } from "@/components/dialogs/delete-user-dialog"
import { useToast } from "@/hooks/use-toast"

interface User {
  id: string
  name: string
  phone: string
  email: string | null
  role: string
  createdAt: string
  updatedAt: string
  studentProfile?: {
    id: string
    level: string
    branch: string
  }
  teacherProfile?: {
    id: string
    subject: string
    experience: number | null
    branch: string
  }
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState("")
  const [loading, setLoading] = useState(false)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [deletingUser, setDeletingUser] = useState<User | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const { toast } = useToast()

  // Fetch users data
  const fetchUsers = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/users")
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users)
        setFilteredUsers(data.users)
      }
    } catch (error) {
      console.error("Error fetching users:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  // Filter users based on search and filters
  useEffect(() => {
    let filtered = users

    if (searchTerm) {
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.phone.includes(searchTerm) ||
          user.email?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (roleFilter) {
      filtered = filtered.filter((user) => user.role === roleFilter)
    }

    setFilteredUsers(filtered)
  }, [users, searchTerm, roleFilter])

  // Get unique roles for filter
  const uniqueRoles = [...new Set(users.map((user) => user.role))]

  // Handle user deletion
  const handleDelete = async (userId: string) => {
    setIsDeleting(true)

    try {
      const response = await fetch(`/api/users?id=${userId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        setUsers(users.filter((user) => user.id !== userId))
        setIsDeleteDialogOpen(false)
        setDeletingUser(null)
        toast({
          title: "Success",
          description: "User deleted successfully",
        })
      } else {
        const error = await response.json()
        throw new Error(error.error || "Failed to delete user")
      }
    } catch (error) {
      console.error("Error deleting user:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete user",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  // Handle form submission
  const handleFormSubmit = () => {
    setIsCreateDialogOpen(false)
    setIsEditDialogOpen(false)
    setEditingUser(null)
    fetchUsers()
  }

  // Handle edit user
  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setIsEditDialogOpen(true)
  }

  // Handle cancel form
  const handleCancelForm = () => {
    setIsCreateDialogOpen(false)
    setIsEditDialogOpen(false)
    setEditingUser(null)
  }

  // Handle delete user
  const handleDeleteUser = (user: User) => {
    setDeletingUser(user)
    setIsDeleteDialogOpen(true)
  }

  // Export to CSV
  const exportToCSV = () => {
    const headers = ["Name", "Phone", "Email", "Role", "Profile Info", "Created"]
    const csvData = filteredUsers.map((user) => [
      user.name,
      user.phone,
      user.email || "",
      user.role,
      user.studentProfile 
        ? `Student - ${user.studentProfile.level} (${user.studentProfile.branch})`
        : user.teacherProfile
        ? `Teacher - ${user.teacherProfile.subject} (${user.teacherProfile.branch})`
        : "",
      new Date(user.createdAt).toLocaleDateString(),
    ])

    const csvContent = [headers, ...csvData]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `users-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Get role badge variant
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "ADMIN":
        return "destructive"
      case "MANAGER":
        return "default"
      case "TEACHER":
        return "secondary"
      case "RECEPTION":
        return "outline"
      case "CASHIER":
        return "outline"
      case "STUDENT":
        return "secondary"
      case "PARENT":
        return "outline"
      default:
        return "secondary"
    }
  }



  // Calculate statistics
  const roleStats = uniqueRoles.map(role => ({
    role,
    count: users.filter(user => user.role === role).length
  }))

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600">Manage system users and their roles</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={exportToCSV} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <UserPlus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New User</DialogTitle>
              </DialogHeader>
              <UserForm
                onSuccess={handleFormSubmit}
                onCancel={handleCancelForm}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="space-y-4">
        {/* Total Users Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <Users className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-lg font-medium text-gray-700">Total Users</p>
                <p className="text-3xl font-bold text-gray-900">{users.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Role Distribution Cards */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {['ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER'].map((role) => {
            const count = users.filter(user => user.role === role).length
            const roleIcons = {
              ADMIN: '👑',
              MANAGER: '📊',
              TEACHER: '🎓',
              RECEPTION: '📞',
              CASHIER: '💰',
              STUDENT: '📚',
              ACADEMIC_MANAGER: '📋'
            }

            const roleColors = {
              ADMIN: 'text-red-600',
              MANAGER: 'text-blue-600',
              TEACHER: 'text-green-600',
              RECEPTION: 'text-purple-600',
              CASHIER: 'text-orange-600',
              STUDENT: 'text-gray-600',
              ACADEMIC_MANAGER: 'text-indigo-600'
            }

            return (
              <Card key={role} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{roleIcons[role as keyof typeof roleIcons]}</div>
                    <div className="flex-1">
                      <p className={`text-sm font-medium ${roleColors[role as keyof typeof roleColors]}`}>
                        {role.replace('_', ' ')}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">{count}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={roleFilter}
          onChange={(e) => setRoleFilter(e.target.value)}
          className="px-3 py-2 border rounded-md"
        >
          <option value="">All Roles</option>
          <option value="ADMIN">Admin</option>
          <option value="MANAGER">Manager</option>
          <option value="TEACHER">Teacher</option>
          <option value="RECEPTION">Reception</option>
          <option value="CASHIER">Cashier</option>
          <option value="STUDENT">Student</option>
          <option value="PARENT">Parent</option>
        </select>
        {(roleFilter || searchTerm) && (
          <Button
            variant="outline"
            onClick={() => {
              setRoleFilter("")
              setSearchTerm("")
            }}
          >
            Clear Filters
          </Button>
        )}
      </div>

      {/* Table */}
      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>
            Manage user accounts and permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Profile</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      Loading users...
                    </TableCell>
                  </TableRow>
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      No users found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-gray-500">ID: {user.id.slice(0, 8)}...</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div>{user.phone}</div>
                          {user.email && (
                            <div className="text-sm text-gray-500">{user.email}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getRoleBadgeVariant(user.role)}>
                          {user.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {user.studentProfile ? (
                          <div className="text-sm">
                            <div>Level: {user.studentProfile.level}</div>
                            <div className="text-gray-500">Branch: {user.studentProfile.branch}</div>
                          </div>
                        ) : user.teacherProfile ? (
                          <div className="text-sm">
                            <div>Subject: {user.teacherProfile.subject}</div>
                            <div className="text-gray-500">Branch: {user.teacherProfile.branch}</div>
                          </div>
                        ) : (
                          <span className="text-gray-400">No profile</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {new Date(user.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteUser(user)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Results count */}
      <div className="text-sm text-gray-500">
        Showing {filteredUsers.length} of {users.length} users
      </div>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <UserForm
            user={editingUser}
            onSuccess={handleFormSubmit}
            onCancel={handleCancelForm}
          />
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <DeleteUserDialog
        user={deletingUser}
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDelete}
        isDeleting={isDeleting}
      />
    </div>
  )
}
