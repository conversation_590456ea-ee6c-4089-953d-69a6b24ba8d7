const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAdminUser() {
  try {
    console.log('Creating admin user...')

    // Hash the password
    const hashedPassword = await bcrypt.hash('Parviz0106$', 12)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { phone: '+998906006299' }
    })

    if (existingUser) {
      console.log('User with this phone number already exists!')
      console.log('Updating existing user to admin role...')
      
      const updatedUser = await prisma.user.update({
        where: { phone: '+998906006299' },
        data: {
          name: 'Admin User',
          role: 'ADMIN',
          password: hashedPassword,
          email: '<EMAIL>'
        }
      })
      
      console.log('✅ Admin user updated successfully!')
      console.log('Phone:', updatedUser.phone)
      console.log('Role:', updatedUser.role)
      console.log('Name:', updatedUser.name)
      return
    }

    // Create new admin user
    const adminUser = await prisma.user.create({
      data: {
        phone: '+998906006299',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'ADMIN',
        password: hashedPassword
      }
    })

    console.log('✅ Admin user created successfully!')
    console.log('Phone:', adminUser.phone)
    console.log('Password: Parviz0106$')
    console.log('Role:', adminUser.role)
    console.log('Name:', adminUser.name)
    console.log('Email:', adminUser.email)

  } catch (error) {
    console.error('❌ Error creating admin user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAdminUser()
