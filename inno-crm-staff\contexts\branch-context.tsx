'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export interface Branch {
  id: string
  name: string
  address?: string
  phone?: string
  isActive: boolean
}

interface BranchContextType {
  currentBranch: Branch
  branches: Branch[]
  switchBranch: (branchId: string) => void
  isLoading: boolean
}

const BranchContext = createContext<BranchContextType | undefined>(undefined)

// Default branches
const DEFAULT_BRANCHES: Branch[] = [
  {
    id: 'main',
    name: 'Main Branch',
    address: 'Gagarin 95A, Samarkand',
    phone: '+998712345678',
    isActive: true
  },
  {
    id: 'branch',
    name: 'Branch',
    address: '<PERSON><PERSON>\'bek 34, Samarkand',
    phone: '+998712345679',
    isActive: true
  }
]

interface BranchProviderProps {
  children: ReactNode
}

export function BranchProvider({ children }: BranchProviderProps) {
  const [currentBranch, setCurrentBranch] = useState<Branch>(DEFAULT_BRANCHES[0])
  const [branches] = useState<Branch[]>(DEFAULT_BRANCHES)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Load saved branch from localStorage
    const savedBranchId = localStorage.getItem('selectedBranch')
    if (savedBranchId) {
      const savedBranch = branches.find(b => b.id === savedBranchId)
      if (savedBranch) {
        setCurrentBranch(savedBranch)
      }
    }
    setIsLoading(false)
  }, [branches])

  const switchBranch = (branchId: string) => {
    const branch = branches.find(b => b.id === branchId)
    if (branch) {
      setCurrentBranch(branch)
      localStorage.setItem('selectedBranch', branchId)
    }
  }

  return (
    <BranchContext.Provider value={{
      currentBranch,
      branches,
      switchBranch,
      isLoading
    }}>
      {children}
    </BranchContext.Provider>
  )
}

export function useBranch() {
  const context = useContext(BranchContext)
  if (context === undefined) {
    throw new Error('useBranch must be used within a BranchProvider')
  }
  return context
}

// Safe version that returns null if context is not available
export function useBranchSafe() {
  const context = useContext(BranchContext)
  return context || null
}
