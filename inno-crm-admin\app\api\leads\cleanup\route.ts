import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Only allow admins to run cleanup
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    // Find archived leads older than 30 days
    const leadsToDelete = await prisma.lead.findMany({
      where: {
        archivedAt: {
          lt: thirtyDaysAgo
        }
      },
      select: {
        id: true,
        name: true,
        phone: true,
        archivedAt: true
      }
    })

    if (leadsToDelete.length === 0) {
      return NextResponse.json({
        message: 'No leads found for cleanup',
        deletedCount: 0
      })
    }

    // Delete call records first (due to foreign key constraints)
    const callRecordsDeleted = await prisma.callRecord.deleteMany({
      where: {
        leadId: {
          in: leadsToDelete.map(lead => lead.id)
        }
      }
    })

    // Delete the leads
    const leadsDeleted = await prisma.lead.deleteMany({
      where: {
        id: {
          in: leadsToDelete.map(lead => lead.id)
        }
      }
    })

    // Log the cleanup activity
    await ActivityLogger.logLeadContacted(
      session.user.id,
      session.user.role as Role,
      'SYSTEM',
      {
        leadName: 'SYSTEM_CLEANUP',
        leadPhone: 'SYSTEM',
        previousStatus: 'ARCHIVED',
        newStatus: 'DELETED',
        notes: `Automatic cleanup: Deleted ${leadsDeleted.count} archived leads older than 30 days and ${callRecordsDeleted.count} associated call records`,
      },
      request
    )

    return NextResponse.json({
      message: 'Cleanup completed successfully',
      deletedLeads: leadsDeleted.count,
      deletedCallRecords: callRecordsDeleted.count,
      leadsDeleted: leadsToDelete.map(lead => ({
        id: lead.id,
        name: lead.name,
        phone: lead.phone,
        archivedAt: lead.archivedAt
      }))
    })
  } catch (error) {
    console.error('Error during cleanup:', error)
    return NextResponse.json(
      { error: 'Internal server error during cleanup' },
      { status: 500 }
    )
  }
}

// Get cleanup preview (what would be deleted)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // Only allow admins to view cleanup preview
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    // Find archived leads older than 30 days
    const leadsToDelete = await prisma.lead.findMany({
      where: {
        archivedAt: {
          lt: thirtyDaysAgo
        }
      },
      include: {
        assignedGroup: {
          include: {
            course: { select: { name: true, level: true } },
            teacher: { include: { user: { select: { name: true } } } }
          }
        },
        callRecords: true
      },
      orderBy: { archivedAt: 'asc' }
    })

    const totalCallRecords = leadsToDelete.reduce((sum, lead) => sum + lead.callRecords.length, 0)

    return NextResponse.json({
      leadsToDelete: leadsToDelete.length,
      callRecordsToDelete: totalCallRecords,
      cutoffDate: thirtyDaysAgo,
      leads: leadsToDelete.map(lead => ({
        id: lead.id,
        name: lead.name,
        phone: lead.phone,
        archivedAt: lead.archivedAt,
        assignedGroup: lead.assignedGroup?.name,
        callRecordsCount: lead.callRecords.length
      }))
    })
  } catch (error) {
    console.error('Error getting cleanup preview:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
