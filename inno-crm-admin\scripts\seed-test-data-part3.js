const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Helper functions
const randomDate = (start, end) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

const randomPhone = () => {
  const prefixes = ['+998901', '+998902', '+998903', '+998904', '+998905', '+998906', '+998907', '+998908', '+998909']
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
  const number = Math.floor(Math.random() * 9000000) + 1000000
  return `${prefix}${number}`
}

const firstNames = [
  '<PERSON>k<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
]

con<PERSON> last<PERSON><PERSON><PERSON> = [
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Otajonov', 'Pulatov', 'Qosimov',
  'Rahimov', 'Saidov', 'Toshmatov', 'Umarov', 'Valiyev', 'Yusupov', 'Zokirov', 'Alimov', 'Botirov'
]

const branches = ['Main Branch', 'Branch']
const levels = ['A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS']
const leadSources = ['Website', 'Social Media', 'Referral', 'Walk-in', 'Phone Call', 'Advertisement']
const coursePreferences = ['General English', 'IELTS Preparation', 'SAT Preparation', 'Mathematics', 'Kids English', 'Business English']

async function generateLeads() {
  console.log('Creating leads...')
  
  const teachers = await prisma.teacher.findMany()
  const groups = await prisma.group.findMany()
  const leads = []
  
  const leadStatuses = ['NEW', 'CALLING', 'CALL_COMPLETED', 'GROUP_ASSIGNED', 'ARCHIVED', 'NOT_INTERESTED']
  const statusWeights = [0.2, 0.15, 0.25, 0.2, 0.1, 0.1] // Distribution of lead statuses

  // Generate 25+ leads
  for (let i = 0; i < 30; i++) {
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]
    
    // Weighted random status selection
    const rand = Math.random()
    let status = 'NEW'
    let cumulative = 0
    for (let j = 0; j < leadStatuses.length; j++) {
      cumulative += statusWeights[j]
      if (rand <= cumulative) {
        status = leadStatuses[j]
        break
      }
    }

    const branch = branches[Math.floor(Math.random() * branches.length)]
    const createdDate = randomDate(new Date(2024, 0, 1), new Date())
    
    const leadData = {
      name: `${firstName} ${lastName}`,
      phone: randomPhone(),
      coursePreference: coursePreferences[Math.floor(Math.random() * coursePreferences.length)],
      status: status,
      source: leadSources[Math.floor(Math.random() * leadSources.length)],
      branch: branch,
      notes: `Interested in ${coursePreferences[Math.floor(Math.random() * coursePreferences.length)]}. ${status === 'NOT_INTERESTED' ? 'Not interested at this time.' : 'Potential student.'}`,
      createdAt: createdDate
    }

    // Add status-specific fields
    if (status === 'CALLING' || status === 'CALL_COMPLETED') {
      leadData.callStartedAt = randomDate(createdDate, new Date())
      if (status === 'CALL_COMPLETED') {
        leadData.callEndedAt = new Date(leadData.callStartedAt.getTime() + Math.random() * 1800000) // 0-30 minutes
        leadData.callDuration = Math.floor((leadData.callEndedAt - leadData.callStartedAt) / 1000) // in seconds
      }
    }

    if (status === 'GROUP_ASSIGNED') {
      const suitableGroups = groups.filter(g => g.branch === branch && g.isActive)
      if (suitableGroups.length > 0) {
        const group = suitableGroups[Math.floor(Math.random() * suitableGroups.length)]
        leadData.assignedGroupId = group.id
        leadData.assignedTeacherId = group.teacherId
        leadData.assignedAt = randomDate(createdDate, new Date())
      }
    }

    if (status === 'ARCHIVED') {
      leadData.archivedAt = randomDate(createdDate, new Date())
    }

    leads.push(leadData)
  }

  const createdLeads = await prisma.lead.createMany({
    data: leads,
    skipDuplicates: true
  })

  console.log(`Created ${createdLeads.count} leads`)
}

async function generateCabinets() {
  console.log('Creating cabinets...')
  
  const cabinets = []
  
  // Create cabinets for both branches
  for (const branch of branches) {
    for (let i = 1; i <= 10; i++) {
      cabinets.push({
        name: `Cabinet ${i}`,
        number: `${branch === 'Main Branch' ? 'M' : 'B'}${String(i).padStart(2, '0')}`,
        capacity: Math.floor(Math.random() * 10) + 15, // 15-25 capacity
        branch: branch,
        isActive: Math.random() > 0.1, // 90% active
        equipment: JSON.stringify([
          'Projector',
          'Whiteboard',
          'Air Conditioning',
          'Sound System'
        ]),
        notes: `Modern classroom with all necessary equipment for ${branch}`
      })
    }
  }

  const createdCabinets = await prisma.cabinet.createMany({
    data: cabinets,
    skipDuplicates: true
  })

  console.log(`Created ${createdCabinets.count} cabinets`)
}

async function generateAssessments() {
  console.log('Creating assessments...')
  
  const students = await prisma.student.findMany()
  const groups = await prisma.group.findMany()
  const teachers = await prisma.teacher.findMany()
  const assessments = []
  
  const assessmentTypes = ['LEVEL_TEST', 'PROGRESS_TEST', 'FINAL_EXAM', 'GROUP_TEST']
  
  // Generate individual assessments for students
  for (let i = 0; i < 20; i++) {
    const student = students[Math.floor(Math.random() * students.length)]
    const teacher = teachers[Math.floor(Math.random() * teachers.length)]
    const type = assessmentTypes[Math.floor(Math.random() * assessmentTypes.length)]
    
    const maxScore = type === 'LEVEL_TEST' ? 100 : (type === 'PROGRESS_TEST' ? 50 : 100)
    const score = Math.floor(Math.random() * maxScore)
    const passed = score >= (maxScore * 0.6) // 60% passing grade
    
    const assignedDate = randomDate(new Date(2024, 0, 1), new Date())
    const startedDate = randomDate(assignedDate, new Date())
    const completedDate = new Date(startedDate.getTime() + Math.random() * 7200000) // 0-2 hours
    
    assessments.push({
      studentId: student.id,
      testName: `${type.replace('_', ' ')} - ${student.level}`,
      type: type,
      level: student.level,
      score: score,
      maxScore: maxScore,
      passed: passed,
      assignedBy: teacher.userId,
      assignedAt: assignedDate,
      startedAt: startedDate,
      completedAt: completedDate,
      questions: JSON.stringify([
        { question: 'Sample question 1', answer: 'Sample answer 1', correct: true },
        { question: 'Sample question 2', answer: 'Sample answer 2', correct: false }
      ]),
      results: JSON.stringify({
        totalQuestions: maxScore / 2,
        correctAnswers: score / 2,
        timeSpent: Math.floor(Math.random() * 120) + 30, // 30-150 minutes
        sections: {
          reading: Math.floor(score * 0.3),
          writing: Math.floor(score * 0.25),
          listening: Math.floor(score * 0.25),
          speaking: Math.floor(score * 0.2)
        }
      })
    })
  }

  // Generate group assessments
  for (let i = 0; i < 5; i++) {
    const group = groups[Math.floor(Math.random() * groups.length)]
    
    assessments.push({
      groupId: group.id,
      testName: `Group Assessment - ${group.name}`,
      type: 'GROUP_TEST',
      assignedBy: group.teacherId,
      assignedAt: randomDate(group.startDate, new Date()),
      questions: JSON.stringify([
        { question: 'Group question 1', options: ['A', 'B', 'C', 'D'], correct: 'A' },
        { question: 'Group question 2', options: ['A', 'B', 'C', 'D'], correct: 'B' }
      ])
    })
  }

  const createdAssessments = await prisma.assessment.createMany({
    data: assessments,
    skipDuplicates: true
  })

  console.log(`Created ${createdAssessments.count} assessments`)
}

async function generateAnnouncements() {
  console.log('Creating announcements...')
  
  const adminUser = await prisma.user.findFirst({ where: { role: 'ADMIN' } })
  const announcements = []
  
  const announcementData = [
    {
      title: 'New IELTS Course Starting',
      content: 'We are excited to announce a new IELTS preparation course starting next month. Limited seats available!',
      priority: 'HIGH',
      targetAudience: 'ALL'
    },
    {
      title: 'Holiday Schedule',
      content: 'Please note that classes will be suspended during the national holidays. Make-up classes will be scheduled.',
      priority: 'MEDIUM',
      targetAudience: 'ALL'
    },
    {
      title: 'Teacher Training Workshop',
      content: 'All teachers are required to attend the professional development workshop this weekend.',
      priority: 'HIGH',
      targetAudience: 'TEACHERS'
    },
    {
      title: 'Payment Reminder',
      content: 'Monthly payments are due by the 5th of each month. Please ensure timely payment to avoid interruption.',
      priority: 'MEDIUM',
      targetAudience: 'STUDENTS'
    },
    {
      title: 'New Academic Manager',
      content: 'We welcome our new Academic Manager who will be overseeing curriculum development and student progress.',
      priority: 'LOW',
      targetAudience: 'ALL'
    }
  ]

  for (const announcement of announcementData) {
    announcements.push({
      ...announcement,
      authorId: adminUser.id,
      isActive: true,
      createdAt: randomDate(new Date(2024, 0, 1), new Date())
    })
  }

  const createdAnnouncements = await prisma.announcement.createMany({
    data: announcements,
    skipDuplicates: true
  })

  console.log(`Created ${createdAnnouncements.count} announcements`)
}

async function main() {
  try {
    console.log('🌱 Starting Phase 3: Leads, Cabinets, Assessments, and Announcements...')
    
    await generateLeads()
    await generateCabinets()
    await generateAssessments()
    await generateAnnouncements()
    
    console.log('✅ Phase 3 complete: All supplementary data created')
    
  } catch (error) {
    console.error('❌ Error in Phase 3:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
