const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testAcademicManagerLogin() {
  try {
    console.log('🔍 Testing Academic Manager login functionality...')
    
    // Find the Academic Manager user
    const academicManager = await prisma.user.findFirst({
      where: {
        role: 'ACADEMIC_MANAGER'
      }
    })
    
    if (!academicManager) {
      console.log('❌ No Academic Manager user found')
      return
    }
    
    console.log('✅ Academic Manager user found:')
    console.log('   ID:', academicManager.id)
    console.log('   Name:', academicManager.name)
    console.log('   Phone:', academicManager.phone)
    console.log('   Email:', academicManager.email)
    console.log('   Role:', academicManager.role)
    console.log('   Created:', academicManager.createdAt)
    
    // Test password verification
    const isPasswordValid = await bcrypt.compare('academic123', academicManager.password)
    console.log('✅ Password verification:', isPasswordValid ? 'PASSED' : 'FAILED')
    
    // Check if user can access assessments
    const assessments = await prisma.assessment.findMany({
      take: 5,
      include: {
        student: {
          include: {
            user: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })
    
    console.log('✅ Assessment access test:')
    console.log('   Found', assessments.length, 'assessments')
    
    if (assessments.length > 0) {
      console.log('   Sample assessment:')
      console.log('     Student:', assessments[0].student.user.name)
      console.log('     Test:', assessments[0].testName)
      console.log('     Score:', assessments[0].score + '/' + assessments[0].maxScore)
      console.log('     Passed:', assessments[0].passed ? 'Yes' : 'No')
    }
    
    // Check students access for statistics
    const students = await prisma.student.findMany({
      take: 5,
      include: {
        user: {
          select: {
            name: true
          }
        },
        assessments: {
          select: {
            testName: true,
            score: true,
            maxScore: true,
            passed: true
          }
        }
      }
    })
    
    console.log('✅ Student statistics access test:')
    console.log('   Found', students.length, 'students')
    
    if (students.length > 0) {
      console.log('   Sample student:')
      console.log('     Name:', students[0].user.name)
      console.log('     Level:', students[0].level)
      console.log('     Assessments:', students[0].assessments.length)
    }
    
    console.log('\n🎉 Academic Manager functionality test completed successfully!')
    console.log('\n📋 Test Credentials for Manual Testing:')
    console.log('   Phone: +998903333333')
    console.log('   Password: academic123')
    console.log('   Expected redirect: /dashboard/assessments')
    
  } catch (error) {
    console.error('❌ Error testing Academic Manager functionality:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAcademicManagerLogin()
