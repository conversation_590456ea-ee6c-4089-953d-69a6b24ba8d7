import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import * as z from 'zod'

const assignGroupSchema = z.object({
  groupId: z.string().min(1, 'Group ID is required'),
  notes: z.string().optional(),
})

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = assignGroupSchema.parse(body)

    // Check if lead exists and is in correct status
    const lead = await prisma.lead.findUnique({
      where: { id },
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    if (lead.status !== 'CALL_COMPLETED') {
      return NextResponse.json(
        { error: 'Lead must be in CALL_COMPLETED status to assign to group' },
        { status: 400 }
      )
    }

    // Check if group exists and has capacity
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
      include: {
        course: { select: { name: true, level: true } },
        teacher: { include: { user: { select: { name: true } } } },
        _count: { select: { enrollments: true } }
      }
    })

    if (!group) {
      return NextResponse.json({ error: 'Group not found' }, { status: 404 })
    }

    if (!group.isActive) {
      return NextResponse.json({ error: 'Group is not active' }, { status: 400 })
    }

    if (group._count.enrollments >= group.capacity) {
      return NextResponse.json({ error: 'Group is at full capacity' }, { status: 400 })
    }

    const now = new Date()

    // Update lead with group assignment
    const updatedLead = await prisma.lead.update({
      where: { id },
      data: {
        status: 'GROUP_ASSIGNED',
        assignedGroupId: validatedData.groupId,
        assignedTeacherId: group.teacherId,
        assignedAt: now,
        notes: validatedData.notes || lead.notes,
        updatedAt: now,
      },
      include: {
        assignedGroup: {
          include: {
            course: { select: { name: true, level: true } },
            teacher: { include: { user: { select: { name: true } } } }
          }
        },
        assignedTeacher: {
          include: { user: { select: { name: true } } }
        }
      }
    })

    // Log activity
    await ActivityLogger.logLeadContacted(
      session.user.id,
      session.user.role as Role,
      lead.id,
      {
        leadName: lead.name,
        leadPhone: lead.phone,
        previousStatus: lead.status,
        newStatus: 'GROUP_ASSIGNED',
        notes: `Assigned to group: ${group.name} (${group.course.name} - ${group.course.level}) with teacher: ${group.teacher.user.name}`,
      },
      request
    )

    return NextResponse.json({
      lead: updatedLead,
      group: group,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error assigning group to lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get available groups for assignment
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url)
    const teacherFilter = searchParams.get('teacher')
    const levelFilter = searchParams.get('level')
    const search = searchParams.get('search')
    const branchId = searchParams.get('branch') || 'main'

    // Map branch ID to branch name for database query
    const branchName = branchId === 'main' ? 'Main Branch' : 'Branch'

    const where: any = {
      isActive: true,
      branch: branchName,
    }

    // Filter by teacher
    if (teacherFilter) {
      where.teacher = {
        user: {
          name: { contains: teacherFilter, mode: 'insensitive' }
        }
      }
    }

    // Filter by level
    if (levelFilter) {
      where.course = {
        level: levelFilter
      }
    }

    // Search filter
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { course: { name: { contains: search, mode: 'insensitive' } } },
        { teacher: { user: { name: { contains: search, mode: 'insensitive' } } } }
      ]
    }

    const groups = await prisma.group.findMany({
      where,
      include: {
        course: { select: { name: true, level: true } },
        teacher: {
          select: {
            id: true,
            tier: true,
            subject: true,
            user: {
              select: { name: true }
            },
          },
        },
        _count: { select: { enrollments: true } }
      },
      orderBy: [
        { startDate: 'asc' },
        { name: 'asc' }
      ]
    })

    // Filter out groups at capacity
    const availableGroups = groups.filter(group => group._count.enrollments < group.capacity)

    // Implement granular tier-based progression per course level, schedule, and time slot
    const tierPriority: Record<string, number> = { 'A_LEVEL': 1, 'B_LEVEL': 2, 'C_LEVEL': 3, 'NEW': 4 }

    // Helper function to extract time and days from schedule
    const parseSchedule = (schedule: string) => {
      try {
        // First try to parse as JSON (for backward compatibility)
        let scheduleStr = schedule
        try {
          const parsed = JSON.parse(schedule)
          scheduleStr = Array.isArray(parsed) ? parsed.join(' ') : schedule
        } catch {
          // If JSON parsing fails, use the string as-is
          scheduleStr = schedule
        }

        // Extract time (e.g., "9:00-11:00" or "14:00-16:00")
        const timeMatch = scheduleStr.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/)
        const time = timeMatch ? `${timeMatch[1]}:${timeMatch[2]}-${timeMatch[3]}:${timeMatch[4]}` : 'Unknown'

        // Extract days (M/W/F or T/T/S)
        const lowerSchedule = scheduleStr.toLowerCase()
        const days = lowerSchedule.includes('monday') &&
                    lowerSchedule.includes('wednesday') &&
                    lowerSchedule.includes('friday') ? 'MWF' :
                    lowerSchedule.includes('tuesday') &&
                    lowerSchedule.includes('thursday') &&
                    lowerSchedule.includes('saturday') ? 'TTS' : 'Other'

        return { time, days }
      } catch (error) {
        console.error('Error parsing schedule:', error, 'Schedule:', schedule)
        return { time: 'Unknown', days: 'Unknown' }
      }
    }

    // Group by course level, schedule pattern, and time slot
    const slotGroups = availableGroups.reduce((acc, group) => {
      const { time, days } = parseSchedule(group.schedule)
      const slotKey = `${group.course.level}-${days}-${time}`

      if (!acc[slotKey]) {
        acc[slotKey] = {
          courseLevel: group.course.level,
          days,
          time,
          groups: []
        }
      }
      acc[slotKey].groups.push(group)
      return acc
    }, {} as Record<string, any>)

    // Apply granular tier progression for each slot
    let finalAvailableGroups: typeof availableGroups = []
    const slotAnalysis: any[] = []

    Object.entries(slotGroups).forEach(([slotKey, slotData]) => {
      // Group by tier within this slot
      const tierGroups = slotData.groups.reduce((acc: any, group: any) => {
        const tier = group.teacher.tier || 'NEW'
        if (!acc[tier]) acc[tier] = []
        acc[tier].push(group)
        return acc
      }, {})

      // Calculate utilization for each tier in this slot
      const tierUtilization = Object.entries(tierGroups).map(([tier, tierGroupList]: [string, any]) => {
        const totalCapacity = tierGroupList.reduce((sum: number, group: any) => sum + group.capacity, 0)
        const totalEnrolled = tierGroupList.reduce((sum: number, group: any) => sum + group._count.enrollments, 0)
        const utilizationRate = totalCapacity > 0 ? (totalEnrolled / totalCapacity) * 100 : 0

        return {
          tier,
          groups: tierGroupList,
          utilizationRate,
          priority: tierPriority[tier] || 4
        }
      }).sort((a, b) => a.priority - b.priority)

      // Apply 80% rule within this specific slot
      const availableInSlot: any[] = []

      for (const tierData of tierUtilization) {
        if (tierData.tier === 'A_LEVEL') {
          // A-Level always available
          availableInSlot.push(...tierData.groups)
        } else {
          // Check if higher tiers in THIS SLOT are 80%+ utilized
          const higherTiersInSlot = tierUtilization.filter(t => t.priority < tierData.priority)
          const allHigherTiersUtilized = higherTiersInSlot.every(t => t.utilizationRate >= 80)

          if (allHigherTiersUtilized || higherTiersInSlot.length === 0) {
            availableInSlot.push(...tierData.groups)
          }
        }
      }

      finalAvailableGroups.push(...availableInSlot)

      slotAnalysis.push({
        slotKey,
        courseLevel: slotData.courseLevel,
        days: slotData.days,
        time: slotData.time,
        tierUtilization: tierUtilization.map(t => ({
          tier: t.tier,
          utilizationRate: Math.round(t.utilizationRate * 100) / 100,
          groupCount: t.groups.length,
          availableGroups: availableInSlot.filter(g => g.teacher.tier === t.tier).length
        })),
        totalAvailable: availableInSlot.length
      })
    })

    // Sort final results by tier priority
    const sortedGroups = finalAvailableGroups.sort((a, b) => {
      const aTier = tierPriority[a.teacher.tier || 'NEW'] || 4
      const bTier = tierPriority[b.teacher.tier || 'NEW'] || 4
      return aTier - bTier
    })

    return NextResponse.json({
      groups: sortedGroups,
      slotAnalysis
    })
  } catch (error) {
    console.error('Error fetching available groups:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
