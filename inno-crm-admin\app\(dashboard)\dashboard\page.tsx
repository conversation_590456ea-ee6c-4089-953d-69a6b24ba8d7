'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ActivityFeed } from '@/components/dashboard/activity-feed'
import {
  Users,
  UserPlus,
  GraduationCap,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  CheckCircle,
  Clock,
  ArrowUpRight,
  BarChart3,
  BookOpen,
  Loader2
} from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'
import { Button } from '@/components/ui/button'

interface DashboardStats {
  totalStudents: { count: number; growth: number }
  newLeads: { count: number; growth: number }
  activeGroups: { count: number }
  monthlyRevenue: { amount: number; growth: number }
  recentLeads: Array<{ name: string; course: string; status: string; time: string }>
  upcomingClasses: Array<{ group: string; teacher: string; time: string; room: string }>
}

export default function DashboardPage() {
  const { currentBranch } = useBranch()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/dashboard/stats')
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard stats')
      }
      const data = await response.json()
      setStats(data)
      setError(null)
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      setError('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('uz-UZ', {
      style: 'currency',
      currency: 'UZS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('UZS', 'UZS')
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchDashboardStats}>Try Again</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 tracking-tight">Welcome to {currentBranch.name}</h1>
          <p className="text-gray-600 mt-1">Here&apos;s what&apos;s happening with your educational center today.</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="shadow-sm">
            <Calendar className="h-4 w-4 mr-2" />
            View Schedule
          </Button>
          <Button className="shadow-sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Total Students</CardTitle>
              <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-blue-700">{formatNumber(stats?.totalStudents.count || 0)}</div>
            <div className={`kpi-change mt-2 ${(stats?.totalStudents.growth ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.totalStudents.growth ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{(stats?.totalStudents.growth ?? 0) >= 0 ? '+' : ''}{stats?.totalStudents.growth || 0}% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">New Leads</CardTitle>
              <div className="h-10 w-10 rounded-full bg-green-50 flex items-center justify-center">
                <UserPlus className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-green-700">{formatNumber(stats?.newLeads.count || 0)}</div>
            <div className={`kpi-change mt-2 ${(stats?.newLeads.growth ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.newLeads.growth ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{(stats?.newLeads.growth ?? 0) >= 0 ? '+' : ''}{stats?.newLeads.growth || 0}% from last week</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Active Groups</CardTitle>
              <div className="h-10 w-10 rounded-full bg-purple-50 flex items-center justify-center">
                <BookOpen className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-purple-700">{formatNumber(stats?.activeGroups.count || 0)}</div>
            <div className="flex items-center justify-between mt-2">
              <div className="kpi-change text-blue-600">
                <CheckCircle className="h-4 w-4" />
                <span>Active groups</span>
              </div>
              <Button variant="ghost" size="sm" className="text-xs text-blue-600 hover:text-blue-700">
                View All
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Monthly Revenue</CardTitle>
              <div className="h-10 w-10 rounded-full bg-amber-50 flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-amber-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-amber-700">{formatCurrency(stats?.monthlyRevenue.amount || 0)}</div>
            <div className={`kpi-change mt-2 ${(stats?.monthlyRevenue.growth ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {(stats?.monthlyRevenue.growth ?? 0) >= 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{(stats?.monthlyRevenue.growth ?? 0) >= 0 ? '+' : ''}{stats?.monthlyRevenue.growth || 0}% from last month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Recent Leads</CardTitle>
                <CardDescription className="mt-1">Latest inquiries from potential students</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <ArrowUpRight className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.recentLeads && stats.recentLeads.length > 0 ? (
                stats.recentLeads.map((lead, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{lead.name}</p>
                      <p className="text-sm text-gray-600">{lead.course}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500 mb-1">{lead.time}</p>
                      <span className={`status-badge ${
                        lead.status === 'NEW' ? 'status-active' :
                        lead.status === 'CONTACTED' ? 'status-pending' :
                        'status-active'
                      }`}>
                        {lead.status}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <UserPlus className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No recent leads</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Upcoming Classes</CardTitle>
                <CardDescription className="mt-1">Today&apos;s scheduled classes</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <Calendar className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {stats?.upcomingClasses && stats.upcomingClasses.length > 0 ? (
                stats.upcomingClasses.map((classItem, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <div>
                      <p className="font-medium text-gray-900">{classItem.group}</p>
                      <p className="text-sm text-gray-600">{classItem.teacher}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-blue-600">{classItem.time}</p>
                      <p className="text-sm text-gray-500">{classItem.room}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No classes scheduled for today</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Activity Feed */}
        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Recent Activity</CardTitle>
                <CardDescription className="mt-1">Latest system activities</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <Clock className="h-4 w-4 mr-2" />
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <ActivityFeed
              limit={8}
              showHeader={false}
              showRefresh={false}
              showViewAll={false}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
