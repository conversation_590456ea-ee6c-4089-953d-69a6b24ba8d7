'use client'

import { useState } from 'react'
import { signOut, useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { BranchSwitcher } from '@/components/ui/branch-switcher'
import { NotificationDropdown } from '@/components/notifications/notification-dropdown'
import { Search, User, LogOut } from 'lucide-react'

export function Header() {
  const { data: session } = useSession()
  const [showUserMenu, setShowUserMenu] = useState(false)

  return (
    <header className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-100">
      <div className="flex items-center justify-between px-8 py-5">
        <div className="flex items-center flex-1 gap-6">
          <BranchSwitcher />
          <div className="relative max-w-lg w-full">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search students, leads, groups..."
              className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 hover:bg-white transition-all duration-200 text-sm font-medium"
            />
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <NotificationDropdown />

          {/* User Menu */}
          <div className="relative">
            <Button
              variant="ghost"
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-3 hover:bg-gray-100 rounded-xl px-4 py-2"
            >
              <div className="h-8 w-8 rounded-full gradient-primary flex items-center justify-center">
                <span className="text-sm font-medium text-white">
                  {session?.user?.name?.charAt(0).toUpperCase() || 'U'}
                </span>
              </div>
              <span className="hidden md:block font-medium text-gray-700">{session?.user?.name || 'User'}</span>
            </Button>

            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg py-2 z-50 border border-gray-100">
                <div className="px-4 py-3 text-sm text-gray-700 border-b border-gray-100">
                  <div className="font-semibold text-gray-900">{session?.user?.name}</div>
                  <div className="text-gray-500 capitalize">{session?.user?.role?.toLowerCase()}</div>
                </div>
                <button
                  onClick={() => signOut()}
                  className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                >
                  <LogOut className="mr-3 h-4 w-4 text-gray-500" />
                  Sign out
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
